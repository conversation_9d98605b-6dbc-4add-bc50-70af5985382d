.\objects\event_groups.o: Mid\FreeRTOS\Source\event_groups.c
.\objects\event_groups.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stdlib.h
.\objects\event_groups.o: .\Mid\FreeRTOS\include\FreeRTOS.h
.\objects\event_groups.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stddef.h
.\objects\event_groups.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stdint.h
.\objects\event_groups.o: .\Mid\FreeRTOS\FreeRTOSConfig.h
.\objects\event_groups.o: .\Mid\FreeRTOS\include\projdefs.h
.\objects\event_groups.o: .\Mid\FreeRTOS\include\portable.h
.\objects\event_groups.o: .\Mid\FreeRTOS\include\deprecated_definitions.h
.\objects\event_groups.o: .\Mid\FreeRTOS\Portable\portmacro.h
.\objects\event_groups.o: .\Mid\FreeRTOS\include\mpu_wrappers.h
.\objects\event_groups.o: .\Mid\FreeRTOS\include\task.h
.\objects\event_groups.o: .\Mid\FreeRTOS\include\list.h
.\objects\event_groups.o: .\Mid\FreeRTOS\include\timers.h
.\objects\event_groups.o: .\Mid\FreeRTOS\include\event_groups.h
