Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to port.o(.emb_text) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to main.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to dri_usart1.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to app_car.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    main.o(i.SysTick_Handler) refers to tasks.o(i.xTaskGetSchedulerState) for xTaskGetSchedulerState
    main.o(i.SysTick_Handler) refers to port.o(i.xPortSysTickHandler) for xPortSysTickHandler
    main.o(i.main) refers to dri_usart1.o(i.Driver_USART1_Init) for Driver_USART1_Init
    main.o(i.main) refers to dri_usart2.o(i.Driver_USART2_Init) for Driver_USART2_Init
    main.o(i.main) refers to int_tb6612.o(i.Int_TB6612_Init) for Int_TB6612_Init
    main.o(i.main) refers to int_encoder.o(i.Int_Encoder_Init) for Int_Encoder_Init
    main.o(i.main) refers to int_mpu6050.o(i.Int_MPU6050_Init) for Int_MPU6050_Init
    main.o(i.main) refers to dri_adc.o(i.Driver_ADC1_Init) for Driver_ADC1_Init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    main.o(i.main) refers to app_task.o(i.App_Task_Init) for App_Task_Init
    dri_usart1.o(i.Driver_USART1_SendString) refers to dri_usart1.o(i.Driver_USART1_SendChar) for Driver_USART1_SendChar
    dri_usart1.o(i.USART1_IRQHandler) refers to dri_usart2.o(i.Driver_USART2_SendString) for Driver_USART2_SendString
    dri_usart1.o(i.USART1_IRQHandler) refers to dri_usart1.o(.bss) for buff
    dri_usart1.o(i.USART1_IRQHandler) refers to dri_usart1.o(.data) for len
    dri_usart1.o(i.fputc) refers to dri_usart1.o(i.Driver_USART1_SendChar) for Driver_USART1_SendChar
    dri_adc.o(i.Driver_ADC1_ReadV) refers to dfltui.o(.text) for __aeabi_ui2d
    dri_adc.o(i.Driver_ADC1_ReadV) refers to dmul.o(.text) for __aeabi_dmul
    dri_adc.o(i.Driver_ADC1_ReadV) refers to ddiv.o(.text) for __aeabi_ddiv
    dri_usart2.o(i.Driver_USART2_SendString) refers to dri_usart2.o(i.Driver_USART2_SendChar) for Driver_USART2_SendChar
    int_tb6612.o(i.Int_TB6612_Init) refers to dri_tim.o(i.Dri_TIM4_Init) for Dri_TIM4_Init
    int_tb6612.o(i.Int_TB6612_SetPWM) refers to int_tb6612.o(i.Int_TB6612_MotorA) for Int_TB6612_MotorA
    int_tb6612.o(i.Int_TB6612_SetPWM) refers to int_tb6612.o(i.Int_TB6612_MotorB) for Int_TB6612_MotorB
    int_encoder.o(i.Int_Encoder_Init) refers to dri_tim.o(i.Dri_TIM2_Init) for Dri_TIM2_Init
    int_encoder.o(i.Int_Encoder_Init) refers to dri_tim.o(i.Dri_TIM3_Init) for Dri_TIM3_Init
    int_mpu6050.o(i.Int_MPU6050_Get_Accel) refers to int_mpu6050.o(i.Int_MPU6050_ReadBytes) for Int_MPU6050_ReadBytes
    int_mpu6050.o(i.Int_MPU6050_Get_Gyro) refers to int_mpu6050.o(i.Int_MPU6050_ReadBytes) for Int_MPU6050_ReadBytes
    int_mpu6050.o(i.Int_MPU6050_Init) refers to dri_i2c.o(i.Driver_I2C2_Init) for Driver_I2C2_Init
    int_mpu6050.o(i.Int_MPU6050_Init) refers to int_mpu6050.o(i.Int_MPU6050_WriteByte) for Int_MPU6050_WriteByte
    int_mpu6050.o(i.Int_MPU6050_Init) refers to main.o(i.for_delay_ms) for for_delay_ms
    int_mpu6050.o(i.Int_MPU6050_Init) refers to int_mpu6050.o(i.Int_MPU6050_ReadByte) for Int_MPU6050_ReadByte
    int_mpu6050.o(i.Int_MPU6050_Init) refers to int_mpu6050.o(i.Int_MPU6050_SetGyroRate) for Int_MPU6050_SetGyroRate
    int_mpu6050.o(i.Int_MPU6050_ReadByte) refers to dri_i2c.o(i.Driver_I2C2_Start) for Driver_I2C2_Start
    int_mpu6050.o(i.Int_MPU6050_ReadByte) refers to dri_i2c.o(i.Driver_I2C_SendAddr) for Driver_I2C_SendAddr
    int_mpu6050.o(i.Int_MPU6050_ReadByte) refers to dri_i2c.o(i.Driver_I2C_SendByte) for Driver_I2C_SendByte
    int_mpu6050.o(i.Int_MPU6050_ReadByte) refers to dri_i2c.o(i.Driver_I2C2_NAck) for Driver_I2C2_NAck
    int_mpu6050.o(i.Int_MPU6050_ReadByte) refers to dri_i2c.o(i.Driver_I2C2_Stop) for Driver_I2C2_Stop
    int_mpu6050.o(i.Int_MPU6050_ReadByte) refers to dri_i2c.o(i.Driver_I2C_ReadByte) for Driver_I2C_ReadByte
    int_mpu6050.o(i.Int_MPU6050_ReadBytes) refers to dri_i2c.o(i.Driver_I2C2_Start) for Driver_I2C2_Start
    int_mpu6050.o(i.Int_MPU6050_ReadBytes) refers to dri_i2c.o(i.Driver_I2C_SendAddr) for Driver_I2C_SendAddr
    int_mpu6050.o(i.Int_MPU6050_ReadBytes) refers to dri_i2c.o(i.Driver_I2C_SendByte) for Driver_I2C_SendByte
    int_mpu6050.o(i.Int_MPU6050_ReadBytes) refers to dri_i2c.o(i.Driver_I2C2_Ack) for Driver_I2C2_Ack
    int_mpu6050.o(i.Int_MPU6050_ReadBytes) refers to dri_i2c.o(i.Driver_I2C2_NAck) for Driver_I2C2_NAck
    int_mpu6050.o(i.Int_MPU6050_ReadBytes) refers to dri_i2c.o(i.Driver_I2C2_Stop) for Driver_I2C2_Stop
    int_mpu6050.o(i.Int_MPU6050_ReadBytes) refers to dri_i2c.o(i.Driver_I2C_ReadByte) for Driver_I2C_ReadByte
    int_mpu6050.o(i.Int_MPU6050_SetGyroRate) refers to int_mpu6050.o(i.Int_MPU6050_WriteByte) for Int_MPU6050_WriteByte
    int_mpu6050.o(i.Int_MPU6050_SetGyroRate) refers to int_mpu6050.o(i.Int_MPU6050_Set_DLPF_CFG) for Int_MPU6050_Set_DLPF_CFG
    int_mpu6050.o(i.Int_MPU6050_Set_DLPF_CFG) refers to int_mpu6050.o(i.Int_MPU6050_WriteByte) for Int_MPU6050_WriteByte
    int_mpu6050.o(i.Int_MPU6050_WriteByte) refers to dri_i2c.o(i.Driver_I2C2_Start) for Driver_I2C2_Start
    int_mpu6050.o(i.Int_MPU6050_WriteByte) refers to dri_i2c.o(i.Driver_I2C_SendAddr) for Driver_I2C_SendAddr
    int_mpu6050.o(i.Int_MPU6050_WriteByte) refers to dri_i2c.o(i.Driver_I2C_SendByte) for Driver_I2C_SendByte
    int_mpu6050.o(i.Int_MPU6050_WriteByte) refers to dri_i2c.o(i.Driver_I2C2_Stop) for Driver_I2C2_Stop
    int_mpu6050.o(i.Int_MPU6050_WriteBytes) refers to dri_i2c.o(i.Driver_I2C2_Start) for Driver_I2C2_Start
    int_mpu6050.o(i.Int_MPU6050_WriteBytes) refers to dri_i2c.o(i.Driver_I2C_SendAddr) for Driver_I2C_SendAddr
    int_mpu6050.o(i.Int_MPU6050_WriteBytes) refers to dri_i2c.o(i.Driver_I2C_SendByte) for Driver_I2C_SendByte
    int_mpu6050.o(i.Int_MPU6050_WriteBytes) refers to dri_i2c.o(i.Driver_I2C2_Stop) for Driver_I2C2_Stop
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ColorTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisplayTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_Init) refers to dri_spi.o(i.Driver_SPI_Init) for Driver_SPI_Init
    oled.o(i.OLED_Init) refers to main.o(i.for_delay_ms) for for_delay_ms
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Refresh) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_ShowChinese) for OLED_ShowChinese
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(.bss) for OLED_GRAM
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for asc2_0806
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChinese) refers to oled.o(.constdata) for Hzk1
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPicture) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    com_filter.o(i.Com_Filter_Kalman) refers to fadd.o(.text) for __aeabi_fsub
    com_filter.o(i.Com_Filter_Kalman) refers to fmul.o(.text) for __aeabi_fmul
    com_filter.o(i.Com_Filter_Kalman) refers to ffltui.o(.text) for __aeabi_ui2f
    com_filter.o(i.Com_Filter_Kalman) refers to fdiv.o(.text) for __aeabi_fdiv
    com_filter.o(i.Com_Filter_Kalman) refers to com_filter.o(.data) for Q_bias
    com_filter.o(i.Com_Filter_Kalman) refers to com_filter.o(.bss) for Pdot
    com_pid.o(i.Com_PID_Balance) refers to fadd.o(.text) for __aeabi_fsub
    com_pid.o(i.Com_PID_Balance) refers to fflti.o(.text) for __aeabi_i2f
    com_pid.o(i.Com_PID_Balance) refers to fmul.o(.text) for __aeabi_fmul
    com_pid.o(i.Com_PID_Balance) refers to ffixi.o(.text) for __aeabi_f2iz
    com_pid.o(i.Com_PID_Turn) refers to fflti.o(.text) for __aeabi_i2f
    com_pid.o(i.Com_PID_Turn) refers to fmul.o(.text) for __aeabi_fmul
    com_pid.o(i.Com_PID_Turn) refers to ffixi.o(.text) for __aeabi_f2iz
    com_pid.o(i.Com_PID_Velocity) refers to dflti.o(.text) for __aeabi_i2d
    com_pid.o(i.Com_PID_Velocity) refers to dmul.o(.text) for __aeabi_dmul
    com_pid.o(i.Com_PID_Velocity) refers to dadd.o(.text) for __aeabi_dadd
    com_pid.o(i.Com_PID_Velocity) refers to dfixi.o(.text) for __aeabi_d2iz
    com_pid.o(i.Com_PID_Velocity) refers to fflti.o(.text) for __aeabi_i2f
    com_pid.o(i.Com_PID_Velocity) refers to fmul.o(.text) for __aeabi_fmul
    com_pid.o(i.Com_PID_Velocity) refers to fadd.o(.text) for __aeabi_fadd
    com_pid.o(i.Com_PID_Velocity) refers to ffixi.o(.text) for __aeabi_f2iz
    com_pid.o(i.Com_PID_Velocity) refers to com_pid.o(.data) for last_velocity
    app_car.o(i.App_Car_Display) refers to dri_adc.o(i.Driver_ADC1_ReadV) for Driver_ADC1_ReadV
    app_car.o(i.App_Car_Display) refers to printfa.o(i.__0sprintf) for __2sprintf
    app_car.o(i.App_Car_Display) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    app_car.o(i.App_Car_Display) refers to f2d.o(.text) for __aeabi_f2d
    app_car.o(i.App_Car_Display) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    app_car.o(i.App_Car_Display) refers to app_car.o(.data) for bat_str
    app_car.o(i.App_Car_Display) refers to com_filter.o(.data) for angle
    app_car.o(i.App_Car_GetAngle) refers to int_mpu6050.o(i.Int_MPU6050_Get_Accel) for Int_MPU6050_Get_Accel
    app_car.o(i.App_Car_GetAngle) refers to int_mpu6050.o(i.Int_MPU6050_Get_Gyro) for Int_MPU6050_Get_Gyro
    app_car.o(i.App_Car_GetAngle) refers to dflti.o(.text) for __aeabi_i2d
    app_car.o(i.App_Car_GetAngle) refers to atan2.o(i.atan2) for atan2
    app_car.o(i.App_Car_GetAngle) refers to dmul.o(.text) for __aeabi_dmul
    app_car.o(i.App_Car_GetAngle) refers to ddiv.o(.text) for __aeabi_ddiv
    app_car.o(i.App_Car_GetAngle) refers to d2f.o(.text) for __aeabi_d2f
    app_car.o(i.App_Car_GetAngle) refers to com_filter.o(i.Com_Filter_Kalman) for Com_Filter_Kalman
    app_car.o(i.App_Car_GetAngle) refers to int_encoder.o(i.Int_Encoder_ReadCounter) for Int_Encoder_ReadCounter
    app_car.o(i.App_Car_GetAngle) refers to app_car.o(.data) for az
    app_car.o(i.App_Car_PID) refers to com_pid.o(i.Com_PID_Balance) for Com_PID_Balance
    app_car.o(i.App_Car_PID) refers to com_pid.o(i.Com_PID_Velocity) for Com_PID_Velocity
    app_car.o(i.App_Car_PID) refers to com_pid.o(i.Com_PID_Turn) for Com_PID_Turn
    app_car.o(i.App_Car_PID) refers to int_tb6612.o(i.Int_TB6612_SetPWM) for Int_TB6612_SetPWM
    app_car.o(i.App_Car_PID) refers to app_car.o(.data) for gy
    app_car.o(i.App_Car_PID) refers to com_filter.o(.data) for angle
    app_car.o(i.USART2_IRQHandler) refers to app_car.o(.data) for flag_up
    app_task.o(i.App_Task_Display) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    app_task.o(i.App_Task_Display) refers to app_car.o(i.App_Car_Display) for App_Car_Display
    app_task.o(i.App_Task_Display) refers to tasks.o(i.xTaskDelayUntil) for xTaskDelayUntil
    app_task.o(i.App_Task_GetData) refers to tasks.o(i.xTaskGetTickCount) for xTaskGetTickCount
    app_task.o(i.App_Task_GetData) refers to app_car.o(i.App_Car_GetAngle) for App_Car_GetAngle
    app_task.o(i.App_Task_GetData) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    app_task.o(i.App_Task_GetData) refers to tasks.o(i.xTaskDelayUntil) for xTaskDelayUntil
    app_task.o(i.App_Task_GetData) refers to app_task.o(.data) for pid_task_handle
    app_task.o(i.App_Task_Init) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    app_task.o(i.App_Task_Init) refers to tasks.o(i.vTaskStartScheduler) for vTaskStartScheduler
    app_task.o(i.App_Task_Init) refers to app_task.o(.data) for start_task_handle
    app_task.o(i.App_Task_Init) refers to app_task.o(i.App_Task_Start) for App_Task_Start
    app_task.o(i.App_Task_PID) refers to tasks.o(i.ulTaskGenericNotifyTake) for ulTaskGenericNotifyTake
    app_task.o(i.App_Task_PID) refers to app_car.o(i.App_Car_PID) for App_Car_PID
    app_task.o(i.App_Task_Start) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    app_task.o(i.App_Task_Start) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    app_task.o(i.App_Task_Start) refers to tasks.o(i.vTaskDelete) for vTaskDelete
    app_task.o(i.App_Task_Start) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    app_task.o(i.App_Task_Start) refers to app_task.o(.data) for data_task_handle
    app_task.o(i.App_Task_Start) refers to app_task.o(i.App_Task_GetData) for App_Task_GetData
    app_task.o(i.App_Task_Start) refers to app_task.o(i.App_Task_PID) for App_Task_PID
    app_task.o(i.App_Task_Start) refers to app_task.o(i.App_Task_Display) for App_Task_Display
    event_groups.o(i.vEventGroupClearBitsCallback) refers to event_groups.o(i.xEventGroupClearBits) for xEventGroupClearBits
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.vEventGroupDelete) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.vEventGroupDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    event_groups.o(i.vEventGroupSetBitsCallback) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupClearBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    event_groups.o(i.xEventGroupCreate) refers to list.o(i.vListInitialise) for vListInitialise
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.vTaskRemoveFromUnorderedEventList) for vTaskRemoveFromUnorderedEventList
    event_groups.o(i.xEventGroupSetBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupSync) refers to event_groups.o(i.xEventGroupSetBits) for xEventGroupSetBits
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupSync) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupSync) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    event_groups.o(i.xEventGroupWaitBits) refers to event_groups.o(i.prvTestWaitCondition) for prvTestWaitCondition
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.vTaskPlaceOnUnorderedEventList) for vTaskPlaceOnUnorderedEventList
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    event_groups.o(i.xEventGroupWaitBits) refers to tasks.o(i.uxTaskResetEventItemValue) for uxTaskResetEventItemValue
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    event_groups.o(i.xEventGroupWaitBits) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvCopyDataFromQueue) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.prvCopyDataToQueue) refers to memcpya.o(.text) for __aeabi_memcpy
    queue.o(i.prvInitialiseNewQueue) refers to queue.o(i.xQueueGenericReset) for xQueueGenericReset
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueEmpty) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvIsQueueFull) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvIsQueueFull) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.prvUnlockQueue) refers to tasks.o(i.vTaskMissedYield) for vTaskMissedYield
    queue.o(i.prvUnlockQueue) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueMessagesWaiting) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.uxQueueSpacesAvailable) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.vQueueDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    queue.o(i.xQueueGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    queue.o(i.xQueueGenericCreate) refers to queue.o(i.prvInitialiseNewQueue) for prvInitialiseNewQueue
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericReset) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericReset) refers to list.o(i.vListInitialise) for vListInitialise
    queue.o(i.xQueueGenericReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvIsQueueFull) for prvIsQueueFull
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueGenericSend) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueGenericSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueGenericSendFromISR) refers to queue.o(i.prvCopyDataToQueue) for prvCopyDataToQueue
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGenericSendFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueGiveFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueuePeek) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueuePeek) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueuePeek) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueuePeek) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueuePeek) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueuePeek) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueuePeek) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueuePeekFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueReceive) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueReceive) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueReceive) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueReceive) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    queue.o(i.xQueueReceiveFromISR) refers to queue.o(i.prvCopyDataFromQueue) for prvCopyDataFromQueue
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueReceiveFromISR) refers to tasks.o(i.uxTaskGetNumberOfTasks) for uxTaskGetNumberOfTasks
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskRemoveFromEventList) for xTaskRemoveFromEventList
    queue.o(i.xQueueSemaphoreTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvIsQueueEmpty) for prvIsQueueEmpty
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.vTaskPlaceOnEventList) for vTaskPlaceOnEventList
    queue.o(i.xQueueSemaphoreTake) refers to queue.o(i.prvUnlockQueue) for prvUnlockQueue
    queue.o(i.xQueueSemaphoreTake) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.prvInitialiseNewStreamBuffer) refers to memseta.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.prvReadBytesFromBuffer) refers to memcpya.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvReadMessageFromBuffer) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.prvWriteBytesToBuffer) refers to memcpya.o(.text) for __aeabi_memcpy
    stream_buffer.o(i.prvWriteMessageToBuffer) refers to stream_buffer.o(i.prvWriteBytesToBuffer) for prvWriteBytesToBuffer
    stream_buffer.o(i.vStreamBufferDelete) refers to heap_4.o(i.vPortFree) for vPortFree
    stream_buffer.o(i.vStreamBufferDelete) refers to memseta.o(.text) for __aeabi_memclr4
    stream_buffer.o(i.xStreamBufferBytesAvailable) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    stream_buffer.o(i.xStreamBufferGenericCreate) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferIsFull) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferNextMessageLengthBytes) refers to stream_buffer.o(i.prvReadBytesFromBuffer) for prvReadBytesFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotifyStateClear) for xTaskGenericNotifyStateClear
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferReceive) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotifyWait) for xTaskGenericNotifyWait
    stream_buffer.o(i.xStreamBufferReceive) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferReceive) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to stream_buffer.o(i.prvReadMessageFromBuffer) for prvReadMessageFromBuffer
    stream_buffer.o(i.xStreamBufferReceiveFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferReset) refers to stream_buffer.o(i.prvInitialiseNewStreamBuffer) for prvInitialiseNewStreamBuffer
    stream_buffer.o(i.xStreamBufferReset) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSetTimeOutState) for vTaskSetTimeOutState
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotifyStateClear) for xTaskGenericNotifyStateClear
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGetCurrentTaskHandle) for xTaskGetCurrentTaskHandle
    stream_buffer.o(i.xStreamBufferSend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotifyWait) for xTaskGenericNotifyWait
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskCheckForTimeOut) for xTaskCheckForTimeOut
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskGenericNotify) for xTaskGenericNotify
    stream_buffer.o(i.xStreamBufferSend) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    stream_buffer.o(i.xStreamBufferSendCompletedFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.xStreamBufferSpacesAvailable) for xStreamBufferSpacesAvailable
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvWriteMessageToBuffer) for prvWriteMessageToBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to stream_buffer.o(i.prvBytesInBuffer) for prvBytesInBuffer
    stream_buffer.o(i.xStreamBufferSendFromISR) refers to tasks.o(i.xTaskGenericNotifyFromISR) for xTaskGenericNotifyFromISR
    tasks.o(i.pcTaskGetName) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.data) for xTickCount
    tasks.o(i.prvAddCurrentTaskToDelayedList) refers to tasks.o(.bss) for xSuspendedTaskList
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(i.prvInitialiseTaskLists) for prvInitialiseTaskLists
    tasks.o(i.prvAddNewTaskToReadyList) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.data) for uxCurrentNumberOfTasks
    tasks.o(i.prvAddNewTaskToReadyList) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.prvCheckTasksWaitingTermination) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.prvCheckTasksWaitingTermination) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.prvCheckTasksWaitingTermination) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(.bss) for xTasksWaitingTermination
    tasks.o(i.prvCheckTasksWaitingTermination) refers to tasks.o(.data) for uxCurrentNumberOfTasks
    tasks.o(i.prvDeleteTCB) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.prvIdleTask) refers to tasks.o(i.prvCheckTasksWaitingTermination) for prvCheckTasksWaitingTermination
    tasks.o(i.prvIdleTask) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.prvInitialiseNewTask) refers to list.o(i.vListInitialiseItem) for vListInitialiseItem
    tasks.o(i.prvInitialiseNewTask) refers to port.o(i.pxPortInitialiseStack) for pxPortInitialiseStack
    tasks.o(i.prvInitialiseTaskLists) refers to list.o(i.vListInitialise) for vListInitialise
    tasks.o(i.prvInitialiseTaskLists) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.prvInitialiseTaskLists) refers to tasks.o(.data) for pxDelayedTaskList
    tasks.o(i.prvResetNextTaskUnblockTime) refers to tasks.o(.data) for pxDelayedTaskList
    tasks.o(i.prvTaskIsTaskSuspended) refers to tasks.o(.bss) for xSuspendedTaskList
    tasks.o(i.ulTaskGenericNotifyTake) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.ulTaskGenericNotifyTake) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskGenericNotifyTake) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.ulTaskGenericNotifyValueClear) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.uxTaskGetNumberOfTasks) refers to tasks.o(.data) for uxCurrentNumberOfTasks
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.uxTaskPriorityGet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.uxTaskPriorityGet) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.uxTaskPriorityGetFromISR) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.uxTaskResetEventItemValue) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskDelay) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.vTaskDelay) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskDelay) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskDelete) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskDelete) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskDelete) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskDelete) refers to tasks.o(i.prvDeleteTCB) for prvDeleteTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskDelete) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskEndScheduler) refers to port.o(i.vPortEndScheduler) for vPortEndScheduler
    tasks.o(i.vTaskEndScheduler) refers to tasks.o(.data) for xSchedulerRunning
    tasks.o(i.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.vTaskGenericNotifyGiveFromISR) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskInternalSetTimeOutState) refers to tasks.o(.data) for xNumOfOverflows
    tasks.o(i.vTaskMissedYield) refers to tasks.o(.data) for xYieldPending
    tasks.o(i.vTaskPlaceOnEventList) refers to list.o(i.vListInsert) for vListInsert
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnEventList) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.vTaskPlaceOnUnorderedEventList) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskPrioritySet) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskPrioritySet) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskPrioritySet) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.data) for uxTopReadyPriority
    tasks.o(i.vTaskRemoveFromUnorderedEventList) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskResume) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskResume) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.vTaskResume) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskResume) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskResume) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskResume) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSetTimeOutState) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSetTimeOutState) refers to tasks.o(.data) for xNumOfOverflows
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.xTaskCreate) for xTaskCreate
    tasks.o(i.vTaskStartScheduler) refers to port.o(i.xPortStartScheduler) for xPortStartScheduler
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(.data) for xIdleTaskHandle
    tasks.o(i.vTaskStartScheduler) refers to tasks.o(i.prvIdleTask) for prvIdleTask
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.vTaskSuspend) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.vTaskSuspend) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.vTaskSuspend) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.vTaskSuspend) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    tasks.o(i.vTaskSuspend) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.vTaskSuspend) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.vTaskSuspendAll) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.vTaskSwitchContext) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.xTaskCatchUpTicks) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCatchUpTicks) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskCatchUpTicks) refers to tasks.o(.data) for xPendedTicks
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(i.vTaskInternalSetTimeOutState) for vTaskInternalSetTimeOutState
    tasks.o(i.xTaskCheckForTimeOut) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskCheckForTimeOut) refers to tasks.o(.data) for xTickCount
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    tasks.o(i.xTaskCreate) refers to memseta.o(.text) for __aeabi_memclr4
    tasks.o(i.xTaskCreate) refers to heap_4.o(i.vPortFree) for vPortFree
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvInitialiseNewTask) for prvInitialiseNewTask
    tasks.o(i.xTaskCreate) refers to tasks.o(i.prvAddNewTaskToReadyList) for prvAddNewTaskToReadyList
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    tasks.o(i.xTaskDelayUntil) refers to tasks.o(.data) for xTickCount
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotify) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.data) for uxTopReadyPriority
    tasks.o(i.xTaskGenericNotify) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskGenericNotifyFromISR) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskGenericNotifyStateClear) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotifyStateClear) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotifyStateClear) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.xTaskGenericNotifyWait) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(i.prvAddCurrentTaskToDelayedList) for prvAddCurrentTaskToDelayedList
    tasks.o(i.xTaskGenericNotifyWait) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskGenericNotifyWait) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.xTaskGetCurrentTaskHandle) refers to tasks.o(.data) for pxCurrentTCB
    tasks.o(i.xTaskGetSchedulerState) refers to tasks.o(.data) for xSchedulerRunning
    tasks.o(i.xTaskGetTickCount) refers to tasks.o(.data) for xTickCount
    tasks.o(i.xTaskGetTickCountFromISR) refers to tasks.o(.data) for xTickCount
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskIncrementTick) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskRemoveFromEventList) refers to tasks.o(.bss) for pxReadyTasksLists
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.prvResetNextTaskUnblockTime) for prvResetNextTaskUnblockTime
    tasks.o(i.xTaskResumeAll) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    tasks.o(i.xTaskResumeAll) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskResumeAll) refers to tasks.o(.bss) for xPendingReadyList
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(i.prvTaskIsTaskSuspended) for prvTaskIsTaskSuspended
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.uxListRemove) for uxListRemove
    tasks.o(i.xTaskResumeFromISR) refers to list.o(i.vListInsertEnd) for vListInsertEnd
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.data) for uxSchedulerSuspended
    tasks.o(i.xTaskResumeFromISR) refers to tasks.o(.bss) for pxReadyTasksLists
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.bss) for ucHeap
    heap_4.o(i.prvHeapInit) refers to heap_4.o(.data) for xStart
    heap_4.o(i.prvInsertBlockIntoFreeList) refers to heap_4.o(.data) for xStart
    heap_4.o(i.pvPortCalloc) refers to heap_4.o(i.pvPortMalloc) for pvPortMalloc
    heap_4.o(i.pvPortCalloc) refers to memseta.o(.text) for __aeabi_memclr
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvHeapInit) for prvHeapInit
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.pvPortMalloc) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.pvPortMalloc) refers to heap_4.o(.data) for pxEnd
    heap_4.o(i.vPortFree) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortFree) refers to heap_4.o(i.prvInsertBlockIntoFreeList) for prvInsertBlockIntoFreeList
    heap_4.o(i.vPortFree) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortFree) refers to heap_4.o(.data) for xFreeBytesRemaining
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.vTaskSuspendAll) for vTaskSuspendAll
    heap_4.o(i.vPortGetHeapStats) refers to tasks.o(i.xTaskResumeAll) for xTaskResumeAll
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortEnterCritical) for vPortEnterCritical
    heap_4.o(i.vPortGetHeapStats) refers to port.o(i.vPortExitCritical) for vPortExitCritical
    heap_4.o(i.vPortGetHeapStats) refers to heap_4.o(.data) for xStart
    heap_4.o(i.xPortGetFreeHeapSize) refers to heap_4.o(.data) for xFreeBytesRemaining
    heap_4.o(i.xPortGetMinimumEverFreeHeapSize) refers to heap_4.o(.data) for xMinimumEverFreeBytesRemaining
    port.o(.emb_text) refers to tasks.o(i.vTaskSwitchContext) for vTaskSwitchContext
    port.o(.emb_text) refers to tasks.o(.data) for pxCurrentTCB
    port.o(i.pxPortInitialiseStack) refers to port.o(i.prvTaskExitError) for prvTaskExitError
    port.o(i.vPortEnterCritical) refers to port.o(.data) for uxCriticalNesting
    port.o(i.vPortExitCritical) refers to port.o(.data) for uxCriticalNesting
    port.o(i.xPortStartScheduler) refers to port.o(i.vPortSetupTimerInterrupt) for vPortSetupTimerInterrupt
    port.o(i.xPortStartScheduler) refers to port.o(.emb_text) for __asm___6_port_c_39a90d8d__prvStartFirstTask
    port.o(i.xPortStartScheduler) refers to port.o(.data) for uxCriticalNesting
    port.o(i.xPortSysTickHandler) refers to tasks.o(i.xTaskIncrementTick) for xTaskIncrementTick
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to dri_usart1.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to dri_usart1.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to dri_usart1.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to dri_usart1.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to dri_usart1.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to dri_usart1.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to dri_usart1.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to dri_usart1.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to dri_usart1.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to dri_usart1.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to dri_usart1.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to dri_usart1.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to dri_usart1.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to dri_usart1.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to dri_usart1.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to dri_usart1.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to dri_usart1.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to dri_usart1.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to dri_usart1.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to dri_usart1.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to dri_usart1.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to dri_usart1.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to dri_usart1.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to dri_usart1.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to dri_usart1.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to dri_usart1.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to dri_usart1.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to dri_usart1.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to dri_usart1.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to dri_usart1.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to dri_usart1.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to dri_usart1.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to dri_usart1.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to dri_usart1.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to dri_usart1.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to dri_usart1.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to dri_usart1.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to dri_usart1.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to dri_usart1.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to dri_usart1.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to dri_usart1.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to dri_usart1.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to dri_usart1.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to dri_usart1.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    ffltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffltui.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.atan) refers to dscalb.o(.text) for __ARM_scalbn
    atan.o(i.atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to dscalb.o(.text) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dscalb.o(.text) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dscalb.o(.text) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing startup_stm32f10x_md.o(HEAP), (512 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing system_stm32f10x.o(.data), (20 bytes).
    Removing dri_usart1.o(i.Driver_USART1_ReceiveChar), (28 bytes).
    Removing dri_usart1.o(i.Driver_USART1_ReceiveString), (52 bytes).
    Removing dri_usart1.o(i.Driver_USART1_SendChar), (28 bytes).
    Removing dri_usart1.o(i.Driver_USART1_SendString), (26 bytes).
    Removing dri_usart1.o(i.fputc), (16 bytes).
    Removing dri_spi.o(i.Driver_SPI_SwapByte), (104 bytes).
    Removing dri_usart2.o(i.Driver_USART2_ReceiveChar), (28 bytes).
    Removing dri_usart2.o(i.Driver_USART2_ReceiveString), (52 bytes).
    Removing int_mpu6050.o(i.Int_MPU6050_WriteBytes), (54 bytes).
    Removing oled.o(i.OLED_ColorTurn), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_Off), (28 bytes).
    Removing oled.o(i.OLED_DisPlay_On), (28 bytes).
    Removing oled.o(i.OLED_DisplayTurn), (44 bytes).
    Removing oled.o(i.OLED_DrawCircle), (168 bytes).
    Removing oled.o(i.OLED_DrawLine), (172 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_ScrollDisplay), (172 bytes).
    Removing oled.o(i.OLED_ShowChinese), (260 bytes).
    Removing oled.o(i.OLED_ShowNum), (140 bytes).
    Removing oled.o(i.OLED_ShowPicture), (214 bytes).
    Removing event_groups.o(i.prvTestWaitCondition), (30 bytes).
    Removing event_groups.o(i.vEventGroupClearBitsCallback), (16 bytes).
    Removing event_groups.o(i.vEventGroupDelete), (42 bytes).
    Removing event_groups.o(i.vEventGroupSetBitsCallback), (16 bytes).
    Removing event_groups.o(i.xEventGroupClearBits), (32 bytes).
    Removing event_groups.o(i.xEventGroupCreate), (26 bytes).
    Removing event_groups.o(i.xEventGroupGetBitsFromISR), (44 bytes).
    Removing event_groups.o(i.xEventGroupSetBits), (146 bytes).
    Removing event_groups.o(i.xEventGroupSync), (168 bytes).
    Removing event_groups.o(i.xEventGroupWaitBits), (204 bytes).
    Removing queue.o(i.prvCopyDataFromQueue), (42 bytes).
    Removing queue.o(i.prvCopyDataToQueue), (106 bytes).
    Removing queue.o(i.prvInitialiseNewQueue), (38 bytes).
    Removing queue.o(i.prvIsQueueEmpty), (26 bytes).
    Removing queue.o(i.prvIsQueueFull), (30 bytes).
    Removing queue.o(i.prvUnlockQueue), (126 bytes).
    Removing queue.o(i.uxQueueMessagesWaiting), (18 bytes).
    Removing queue.o(i.uxQueueMessagesWaitingFromISR), (8 bytes).
    Removing queue.o(i.uxQueueSpacesAvailable), (24 bytes).
    Removing queue.o(i.vQueueDelete), (14 bytes).
    Removing queue.o(i.xQueueGenericCreate), (76 bytes).
    Removing queue.o(i.xQueueGenericReset), (160 bytes).
    Removing queue.o(i.xQueueGenericSend), (280 bytes).
    Removing queue.o(i.xQueueGenericSendFromISR), (152 bytes).
    Removing queue.o(i.xQueueGiveFromISR), (132 bytes).
    Removing queue.o(i.xQueueIsQueueEmptyFromISR), (16 bytes).
    Removing queue.o(i.xQueueIsQueueFullFromISR), (22 bytes).
    Removing queue.o(i.xQueuePeek), (268 bytes).
    Removing queue.o(i.xQueuePeekFromISR), (74 bytes).
    Removing queue.o(i.xQueueReceive), (260 bytes).
    Removing queue.o(i.xQueueReceiveFromISR), (140 bytes).
    Removing queue.o(i.xQueueSemaphoreTake), (240 bytes).
    Removing stream_buffer.o(i.prvBytesInBuffer), (24 bytes).
    Removing stream_buffer.o(i.prvInitialiseNewStreamBuffer), (42 bytes).
    Removing stream_buffer.o(i.prvReadBytesFromBuffer), (76 bytes).
    Removing stream_buffer.o(i.prvReadMessageFromBuffer), (90 bytes).
    Removing stream_buffer.o(i.prvWriteBytesToBuffer), (76 bytes).
    Removing stream_buffer.o(i.prvWriteMessageToBuffer), (88 bytes).
    Removing stream_buffer.o(i.vStreamBufferDelete), (32 bytes).
    Removing stream_buffer.o(i.xStreamBufferBytesAvailable), (18 bytes).
    Removing stream_buffer.o(i.xStreamBufferGenericCreate), (82 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsEmpty), (22 bytes).
    Removing stream_buffer.o(i.xStreamBufferIsFull), (40 bytes).
    Removing stream_buffer.o(i.xStreamBufferNextMessageLengthBytes), (52 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceive), (176 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveCompletedFromISR), (80 bytes).
    Removing stream_buffer.o(i.xStreamBufferReceiveFromISR), (132 bytes).
    Removing stream_buffer.o(i.xStreamBufferReset), (56 bytes).
    Removing stream_buffer.o(i.xStreamBufferSend), (226 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendCompletedFromISR), (80 bytes).
    Removing stream_buffer.o(i.xStreamBufferSendFromISR), (134 bytes).
    Removing stream_buffer.o(i.xStreamBufferSetTriggerLevel), (26 bytes).
    Removing stream_buffer.o(i.xStreamBufferSpacesAvailable), (40 bytes).
    Removing tasks.o(i.pcTaskGetName), (24 bytes).
    Removing tasks.o(i.prvTaskIsTaskSuspended), (64 bytes).
    Removing tasks.o(i.ulTaskGenericNotifyValueClear), (64 bytes).
    Removing tasks.o(i.uxTaskGetNumberOfTasks), (12 bytes).
    Removing tasks.o(i.uxTaskPriorityGet), (36 bytes).
    Removing tasks.o(i.uxTaskPriorityGetFromISR), (56 bytes).
    Removing tasks.o(i.uxTaskResetEventItemValue), (28 bytes).
    Removing tasks.o(i.vTaskDelay), (52 bytes).
    Removing tasks.o(i.vTaskEndScheduler), (36 bytes).
    Removing tasks.o(i.vTaskGenericNotifyGiveFromISR), (336 bytes).
    Removing tasks.o(i.vTaskInternalSetTimeOutState), (24 bytes).
    Removing tasks.o(i.vTaskMissedYield), (12 bytes).
    Removing tasks.o(i.vTaskPlaceOnEventList), (32 bytes).
    Removing tasks.o(i.vTaskPlaceOnUnorderedEventList), (80 bytes).
    Removing tasks.o(i.vTaskPrioritySet), (284 bytes).
    Removing tasks.o(i.vTaskRemoveFromUnorderedEventList), (208 bytes).
    Removing tasks.o(i.vTaskResume), (172 bytes).
    Removing tasks.o(i.vTaskSetTimeOutState), (36 bytes).
    Removing tasks.o(i.vTaskSuspend), (216 bytes).
    Removing tasks.o(i.xTaskCatchUpTicks), (40 bytes).
    Removing tasks.o(i.xTaskCheckForTimeOut), (96 bytes).
    Removing tasks.o(i.xTaskGenericNotifyFromISR), (504 bytes).
    Removing tasks.o(i.xTaskGenericNotifyStateClear), (64 bytes).
    Removing tasks.o(i.xTaskGenericNotifyWait), (172 bytes).
    Removing tasks.o(i.xTaskGetCurrentTaskHandle), (12 bytes).
    Removing tasks.o(i.xTaskGetTickCountFromISR), (12 bytes).
    Removing tasks.o(i.xTaskRemoveFromEventList), (264 bytes).
    Removing tasks.o(i.xTaskResumeFromISR), (216 bytes).
    Removing heap_4.o(i.pvPortCalloc), (54 bytes).
    Removing heap_4.o(i.vPortGetHeapStats), (128 bytes).
    Removing heap_4.o(i.vPortInitialiseBlocks), (2 bytes).
    Removing heap_4.o(i.xPortGetFreeHeapSize), (12 bytes).
    Removing heap_4.o(i.xPortGetMinimumEverFreeHeapSize), (12 bytes).
    Removing port.o(i.vPortEndScheduler), (2 bytes).

111 unused section(s) (total 10322 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/../cmprslib/zerorunl.c           0x00000000   Number         0  __dczerorl.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  ffltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  dscalb.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    App\App_Car.c                            0x00000000   Number         0  app_car.o ABSOLUTE
    App\App_Task.c                           0x00000000   Number         0  app_task.o ABSOLUTE
    Com\Com_Filter.c                         0x00000000   Number         0  com_filter.o ABSOLUTE
    Com\Com_PID.c                            0x00000000   Number         0  com_pid.o ABSOLUTE
    Dri\Dri_ADC.c                            0x00000000   Number         0  dri_adc.o ABSOLUTE
    Dri\Dri_I2C.c                            0x00000000   Number         0  dri_i2c.o ABSOLUTE
    Dri\Dri_SPI.c                            0x00000000   Number         0  dri_spi.o ABSOLUTE
    Dri\Dri_TIM.c                            0x00000000   Number         0  dri_tim.o ABSOLUTE
    Dri\Dri_USART1.c                         0x00000000   Number         0  dri_usart1.o ABSOLUTE
    Dri\Dri_USART2.c                         0x00000000   Number         0  dri_usart2.o ABSOLUTE
    Int\Int_Encoder.c                        0x00000000   Number         0  int_encoder.o ABSOLUTE
    Int\Int_MPU6050.c                        0x00000000   Number         0  int_mpu6050.o ABSOLUTE
    Int\Int_TB6612.c                         0x00000000   Number         0  int_tb6612.o ABSOLUTE
    Int\OLED\oled.c                          0x00000000   Number         0  oled.o ABSOLUTE
    Mid\FreeRTOS\Portable\heap_4.c           0x00000000   Number         0  heap_4.o ABSOLUTE
    Mid\FreeRTOS\Portable\port.c             0x00000000   Number         0  port.o ABSOLUTE
    Mid\FreeRTOS\Source\croutine.c           0x00000000   Number         0  croutine.o ABSOLUTE
    Mid\FreeRTOS\Source\event_groups.c       0x00000000   Number         0  event_groups.o ABSOLUTE
    Mid\FreeRTOS\Source\list.c               0x00000000   Number         0  list.o ABSOLUTE
    Mid\FreeRTOS\Source\queue.c              0x00000000   Number         0  queue.o ABSOLUTE
    Mid\FreeRTOS\Source\stream_buffer.c      0x00000000   Number         0  stream_buffer.o ABSOLUTE
    Mid\FreeRTOS\Source\tasks.c              0x00000000   Number         0  tasks.o ABSOLUTE
    Mid\FreeRTOS\Source\timers.c             0x00000000   Number         0  timers.o ABSOLUTE
    Mid\\FreeRTOS\\Portable\\port.c          0x00000000   Number         0  port.o ABSOLUTE
    Start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    Start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    .ARM.Collect$$$$00000000                 0x080000ec   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080000ec   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080000f0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080000f4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080000f4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080000f4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x080000fc   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x08000100   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x08000100   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x08000100   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000100   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .emb_text                                0x08000104   Section      150  port.o(.emb_text)
    .text                                    0x0800019c   Section       36  startup_stm32f10x_md.o(.text)
    .text                                    0x080001c0   Section        0  memseta.o(.text)
    .text                                    0x080001e4   Section        0  fadd.o(.text)
    .text                                    0x08000294   Section        0  fmul.o(.text)
    .text                                    0x080002f8   Section        0  fdiv.o(.text)
    .text                                    0x08000374   Section        0  dadd.o(.text)
    .text                                    0x080004c2   Section        0  dmul.o(.text)
    .text                                    0x080005a6   Section        0  ddiv.o(.text)
    .text                                    0x08000684   Section        0  fflti.o(.text)
    .text                                    0x08000696   Section        0  ffltui.o(.text)
    .text                                    0x080006a0   Section        0  dflti.o(.text)
    .text                                    0x080006c2   Section        0  dfltui.o(.text)
    .text                                    0x080006dc   Section        0  ffixi.o(.text)
    .text                                    0x0800070e   Section        0  dfixi.o(.text)
    .text                                    0x0800074c   Section        0  f2d.o(.text)
    .text                                    0x08000772   Section        0  d2f.o(.text)
    .text                                    0x080007aa   Section        0  uidiv.o(.text)
    .text                                    0x080007d6   Section        0  uldiv.o(.text)
    .text                                    0x08000838   Section        0  llshl.o(.text)
    .text                                    0x08000856   Section        0  llushr.o(.text)
    .text                                    0x08000876   Section        0  llsshr.o(.text)
    .text                                    0x0800089a   Section        0  iusefp.o(.text)
    .text                                    0x0800089a   Section        0  fepilogue.o(.text)
    .text                                    0x08000908   Section        0  depilogue.o(.text)
    .text                                    0x080009c2   Section        0  dfixul.o(.text)
    .text                                    0x080009f4   Section       48  cdcmple.o(.text)
    .text                                    0x08000a24   Section       48  cdrcmple.o(.text)
    .text                                    0x08000a54   Section       36  init.o(.text)
    .text                                    0x08000a78   Section        0  dscalb.o(.text)
    .text                                    0x08000aa6   Section        0  __dczerorl.o(.text)
    i.App_Car_Display                        0x08000ae0   Section        0  app_car.o(i.App_Car_Display)
    i.App_Car_GetAngle                       0x08000b9c   Section        0  app_car.o(i.App_Car_GetAngle)
    i.App_Car_PID                            0x08000c78   Section        0  app_car.o(i.App_Car_PID)
    i.App_Task_Display                       0x08000dac   Section        0  app_task.o(i.App_Task_Display)
    i.App_Task_GetData                       0x08000dc8   Section        0  app_task.o(i.App_Task_GetData)
    i.App_Task_Init                          0x08000dfc   Section        0  app_task.o(i.App_Task_Init)
    i.App_Task_PID                           0x08000e30   Section        0  app_task.o(i.App_Task_PID)
    i.App_Task_Start                         0x08000e44   Section        0  app_task.o(i.App_Task_Start)
    i.Com_Filter_Kalman                      0x08000ee4   Section        0  com_filter.o(i.Com_Filter_Kalman)
    i.Com_PID_Balance                        0x0800115c   Section        0  com_pid.o(i.Com_PID_Balance)
    i.Com_PID_Turn                           0x080011a4   Section        0  com_pid.o(i.Com_PID_Turn)
    i.Com_PID_Velocity                       0x080011c4   Section        0  com_pid.o(i.Com_PID_Velocity)
    i.Dri_TIM2_Init                          0x0800129c   Section        0  dri_tim.o(i.Dri_TIM2_Init)
    i.Dri_TIM3_Init                          0x080013a0   Section        0  dri_tim.o(i.Dri_TIM3_Init)
    i.Dri_TIM4_Init                          0x080014bc   Section        0  dri_tim.o(i.Dri_TIM4_Init)
    i.Driver_ADC1_Init                       0x080015e4   Section        0  dri_adc.o(i.Driver_ADC1_Init)
    i.Driver_ADC1_ReadV                      0x080016ec   Section        0  dri_adc.o(i.Driver_ADC1_ReadV)
    i.Driver_I2C2_Ack                        0x08001754   Section        0  dri_i2c.o(i.Driver_I2C2_Ack)
    i.Driver_I2C2_Init                       0x08001768   Section        0  dri_i2c.o(i.Driver_I2C2_Init)
    i.Driver_I2C2_NAck                       0x080017e4   Section        0  dri_i2c.o(i.Driver_I2C2_NAck)
    i.Driver_I2C2_Start                      0x080017f8   Section        0  dri_i2c.o(i.Driver_I2C2_Start)
    i.Driver_I2C2_Stop                       0x0800182c   Section        0  dri_i2c.o(i.Driver_I2C2_Stop)
    i.Driver_I2C_ReadByte                    0x08001840   Section        0  dri_i2c.o(i.Driver_I2C_ReadByte)
    i.Driver_I2C_SendAddr                    0x0800186c   Section        0  dri_i2c.o(i.Driver_I2C_SendAddr)
    i.Driver_I2C_SendByte                    0x080018a4   Section        0  dri_i2c.o(i.Driver_I2C_SendByte)
    i.Driver_SPI_Init                        0x080018ec   Section        0  dri_spi.o(i.Driver_SPI_Init)
    i.Driver_USART1_Init                     0x08001968   Section        0  dri_usart1.o(i.Driver_USART1_Init)
    i.Driver_USART2_Init                     0x08001a94   Section        0  dri_usart2.o(i.Driver_USART2_Init)
    i.Driver_USART2_SendChar                 0x08001bb8   Section        0  dri_usart2.o(i.Driver_USART2_SendChar)
    i.Driver_USART2_SendString               0x08001bd4   Section        0  dri_usart2.o(i.Driver_USART2_SendString)
    i.Int_Encoder_Init                       0x08001bee   Section        0  int_encoder.o(i.Int_Encoder_Init)
    i.Int_Encoder_ReadCounter                0x08001bfc   Section        0  int_encoder.o(i.Int_Encoder_ReadCounter)
    i.Int_MPU6050_Get_Accel                  0x08001c34   Section        0  int_mpu6050.o(i.Int_MPU6050_Get_Accel)
    i.Int_MPU6050_Get_Gyro                   0x08001c78   Section        0  int_mpu6050.o(i.Int_MPU6050_Get_Gyro)
    i.Int_MPU6050_Init                       0x08001cbc   Section        0  int_mpu6050.o(i.Int_MPU6050_Init)
    i.Int_MPU6050_ReadByte                   0x08001d2e   Section        0  int_mpu6050.o(i.Int_MPU6050_ReadByte)
    i.Int_MPU6050_ReadBytes                  0x08001d60   Section        0  int_mpu6050.o(i.Int_MPU6050_ReadBytes)
    i.Int_MPU6050_SetGyroRate                0x08001db0   Section        0  int_mpu6050.o(i.Int_MPU6050_SetGyroRate)
    i.Int_MPU6050_Set_DLPF_CFG               0x08001de4   Section        0  int_mpu6050.o(i.Int_MPU6050_Set_DLPF_CFG)
    i.Int_MPU6050_WriteByte                  0x08001e28   Section        0  int_mpu6050.o(i.Int_MPU6050_WriteByte)
    i.Int_TB6612_Init                        0x08001e4c   Section        0  int_tb6612.o(i.Int_TB6612_Init)
    i.Int_TB6612_MotorA                      0x08001e78   Section        0  int_tb6612.o(i.Int_TB6612_MotorA)
    i.Int_TB6612_MotorB                      0x08001ecc   Section        0  int_tb6612.o(i.Int_TB6612_MotorB)
    i.Int_TB6612_SetPWM                      0x08001f20   Section        0  int_tb6612.o(i.Int_TB6612_SetPWM)
    i.OLED_Clear                             0x08001f78   Section        0  oled.o(i.OLED_Clear)
    i.OLED_DrawPoint                         0x08001fa8   Section        0  oled.o(i.OLED_DrawPoint)
    i.OLED_Init                              0x08002020   Section        0  oled.o(i.OLED_Init)
    i.OLED_Refresh                           0x0800215c   Section        0  oled.o(i.OLED_Refresh)
    i.OLED_ShowChar                          0x080021a8   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowString                        0x080022e4   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WR_Byte                           0x08002330   Section        0  oled.o(i.OLED_WR_Byte)
    i.SetSysClock                            0x080023c4   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x080023c5   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x080023cc   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x080023cd   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_Handler                        0x080024ac   Section        0  main.o(i.SysTick_Handler)
    i.SystemInit                             0x080024bc   Section        0  system_stm32f10x.o(i.SystemInit)
    i.USART1_IRQHandler                      0x0800251c   Section        0  dri_usart1.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08002580   Section        0  app_car.o(i.USART2_IRQHandler)
    i.__0sprintf                             0x0800264c   Section        0  printfa.o(i.__0sprintf)
    i.__ARM_fpclassify                       0x08002674   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__kernel_poly                          0x0800269c   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_infnan                   0x08002746   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x0800274c   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_underflow                0x08002750   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x08002760   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800276e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08002770   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08002780   Section        0  errno.o(i.__set_errno)
    i._fp_digits                             0x0800278c   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x0800278d   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08002910   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08002911   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x08002fc4   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x08002fc5   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08002fe8   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08002fe9   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x08003016   Section        0  printfa.o(i._sputc)
    _sputc                                   0x08003017   Thumb Code    10  printfa.o(i._sputc)
    i.atan                                   0x08003020   Section        0  atan.o(i.atan)
    i.atan2                                  0x08003240   Section        0  atan2.o(i.atan2)
    i.for_delay_ms                           0x080033dc   Section        0  main.o(i.for_delay_ms)
    i.main                                   0x080033f8   Section        0  main.o(i.main)
    i.prvAddCurrentTaskToDelayedList         0x08003488   Section        0  tasks.o(i.prvAddCurrentTaskToDelayedList)
    prvAddCurrentTaskToDelayedList           0x08003489   Thumb Code   164  tasks.o(i.prvAddCurrentTaskToDelayedList)
    i.prvAddNewTaskToReadyList               0x08003548   Section        0  tasks.o(i.prvAddNewTaskToReadyList)
    prvAddNewTaskToReadyList                 0x08003549   Thumb Code   200  tasks.o(i.prvAddNewTaskToReadyList)
    i.prvCheckTasksWaitingTermination        0x0800362c   Section        0  tasks.o(i.prvCheckTasksWaitingTermination)
    prvCheckTasksWaitingTermination          0x0800362d   Thumb Code    60  tasks.o(i.prvCheckTasksWaitingTermination)
    i.prvDeleteTCB                           0x08003674   Section        0  tasks.o(i.prvDeleteTCB)
    prvDeleteTCB                             0x08003675   Thumb Code    18  tasks.o(i.prvDeleteTCB)
    i.prvHeapInit                            0x08003688   Section        0  heap_4.o(i.prvHeapInit)
    prvHeapInit                              0x08003689   Thumb Code    90  heap_4.o(i.prvHeapInit)
    i.prvIdleTask                            0x080036f8   Section        0  tasks.o(i.prvIdleTask)
    prvIdleTask                              0x080036f9   Thumb Code    32  tasks.o(i.prvIdleTask)
    i.prvInitialiseNewTask                   0x08003720   Section        0  tasks.o(i.prvInitialiseNewTask)
    prvInitialiseNewTask                     0x08003721   Thumb Code   122  tasks.o(i.prvInitialiseNewTask)
    i.prvInitialiseTaskLists                 0x0800379c   Section        0  tasks.o(i.prvInitialiseTaskLists)
    prvInitialiseTaskLists                   0x0800379d   Thumb Code    70  tasks.o(i.prvInitialiseTaskLists)
    i.prvInsertBlockIntoFreeList             0x08003804   Section        0  heap_4.o(i.prvInsertBlockIntoFreeList)
    prvInsertBlockIntoFreeList               0x08003805   Thumb Code    96  heap_4.o(i.prvInsertBlockIntoFreeList)
    i.prvResetNextTaskUnblockTime            0x0800386c   Section        0  tasks.o(i.prvResetNextTaskUnblockTime)
    prvResetNextTaskUnblockTime              0x0800386d   Thumb Code    40  tasks.o(i.prvResetNextTaskUnblockTime)
    i.prvTaskExitError                       0x0800389c   Section        0  port.o(i.prvTaskExitError)
    prvTaskExitError                         0x0800389d   Thumb Code    22  port.o(i.prvTaskExitError)
    i.pvPortMalloc                           0x080038b4   Section        0  heap_4.o(i.pvPortMalloc)
    i.pxPortInitialiseStack                  0x08003998   Section        0  port.o(i.pxPortInitialiseStack)
    i.ulTaskGenericNotifyTake                0x080039bc   Section        0  tasks.o(i.ulTaskGenericNotifyTake)
    i.uxListRemove                           0x08003a44   Section        0  list.o(i.uxListRemove)
    i.vListInitialise                        0x08003a6c   Section        0  list.o(i.vListInitialise)
    i.vListInitialiseItem                    0x08003a86   Section        0  list.o(i.vListInitialiseItem)
    i.vListInsert                            0x08003a8c   Section        0  list.o(i.vListInsert)
    i.vListInsertEnd                         0x08003ac0   Section        0  list.o(i.vListInsertEnd)
    i.vPortEnterCritical                     0x08003ad8   Section        0  port.o(i.vPortEnterCritical)
    i.vPortExitCritical                      0x08003afc   Section        0  port.o(i.vPortExitCritical)
    i.vPortFree                              0x08003b1c   Section        0  heap_4.o(i.vPortFree)
    i.vPortSetupTimerInterrupt               0x08003b68   Section        0  port.o(i.vPortSetupTimerInterrupt)
    i.vTaskDelete                            0x08003b80   Section        0  tasks.o(i.vTaskDelete)
    i.vTaskStartScheduler                    0x08003c54   Section        0  tasks.o(i.vTaskStartScheduler)
    i.vTaskSuspendAll                        0x08003cc0   Section        0  tasks.o(i.vTaskSuspendAll)
    i.vTaskSwitchContext                     0x08003cd0   Section        0  tasks.o(i.vTaskSwitchContext)
    i.xPortStartScheduler                    0x08003d30   Section        0  port.o(i.xPortStartScheduler)
    i.xPortSysTickHandler                    0x08003d64   Section        0  port.o(i.xPortSysTickHandler)
    i.xTaskCreate                            0x08003d98   Section        0  tasks.o(i.xTaskCreate)
    i.xTaskDelayUntil                        0x08003e00   Section        0  tasks.o(i.xTaskDelayUntil)
    i.xTaskGenericNotify                     0x08003e74   Section        0  tasks.o(i.xTaskGenericNotify)
    i.xTaskGetSchedulerState                 0x08003fc0   Section        0  tasks.o(i.xTaskGetSchedulerState)
    i.xTaskGetTickCount                      0x08003fe0   Section        0  tasks.o(i.xTaskGetTickCount)
    i.xTaskIncrementTick                     0x08003fec   Section        0  tasks.o(i.xTaskIncrementTick)
    i.xTaskResumeAll                         0x0800417c   Section        0  tasks.o(i.xTaskResumeAll)
    .constdata                               0x080042d8   Section     7696  oled.o(.constdata)
    .constdata                               0x080060e8   Section      152  atan.o(.constdata)
    atanhi                                   0x080060e8   Data          32  atan.o(.constdata)
    atanlo                                   0x08006108   Data          32  atan.o(.constdata)
    aTodd                                    0x08006128   Data          40  atan.o(.constdata)
    aTeven                                   0x08006150   Data          48  atan.o(.constdata)
    .constdata                               0x08006180   Section        8  qnan.o(.constdata)
    .data                                    0x20000000   Section        2  dri_usart1.o(.data)
    .data                                    0x20000004   Section       84  com_filter.o(.data)
    .data                                    0x20000058   Section        8  com_pid.o(.data)
    least_velocity                           0x20000058   Data           4  com_pid.o(.data)
    last_velocity                            0x2000005c   Data           4  com_pid.o(.data)
    .data                                    0x20000060   Section       92  app_car.o(.data)
    .data                                    0x200000bc   Section       16  app_task.o(.data)
    .data                                    0x200000cc   Section       64  tasks.o(.data)
    pxDelayedTaskList                        0x200000d0   Data           4  tasks.o(.data)
    pxOverflowDelayedTaskList                0x200000d4   Data           4  tasks.o(.data)
    uxDeletedTasksWaitingCleanUp             0x200000d8   Data           4  tasks.o(.data)
    uxCurrentNumberOfTasks                   0x200000dc   Data           4  tasks.o(.data)
    xTickCount                               0x200000e0   Data           4  tasks.o(.data)
    uxTopReadyPriority                       0x200000e4   Data           4  tasks.o(.data)
    xSchedulerRunning                        0x200000e8   Data           4  tasks.o(.data)
    xPendedTicks                             0x200000ec   Data           4  tasks.o(.data)
    xYieldPending                            0x200000f0   Data           4  tasks.o(.data)
    xNumOfOverflows                          0x200000f4   Data           4  tasks.o(.data)
    uxTaskNumber                             0x200000f8   Data           4  tasks.o(.data)
    xNextTaskUnblockTime                     0x200000fc   Data           4  tasks.o(.data)
    xIdleTaskHandle                          0x20000100   Data           4  tasks.o(.data)
    uxSchedulerSuspended                     0x20000108   Data           4  tasks.o(.data)
    .data                                    0x2000010c   Section       28  heap_4.o(.data)
    xStart                                   0x2000010c   Data           8  heap_4.o(.data)
    pxEnd                                    0x20000114   Data           4  heap_4.o(.data)
    xFreeBytesRemaining                      0x20000118   Data           4  heap_4.o(.data)
    xMinimumEverFreeBytesRemaining           0x2000011c   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulAllocations           0x20000120   Data           4  heap_4.o(.data)
    xNumberOfSuccessfulFrees                 0x20000124   Data           4  heap_4.o(.data)
    .data                                    0x20000128   Section        4  port.o(.data)
    uxCriticalNesting                        0x20000128   Data           4  port.o(.data)
    .data                                    0x2000012c   Section        4  errno.o(.data)
    _errno                                   0x2000012c   Data           4  errno.o(.data)
    .bss                                     0x20000130   Section      100  dri_usart1.o(.bss)
    .bss                                     0x20000194   Section     1152  oled.o(.bss)
    .bss                                     0x20000614   Section       16  com_filter.o(.bss)
    .bss                                     0x20000624   Section      740  tasks.o(.bss)
    pxReadyTasksLists                        0x20000624   Data         640  tasks.o(.bss)
    xDelayedTaskList1                        0x200008a4   Data          20  tasks.o(.bss)
    xDelayedTaskList2                        0x200008b8   Data          20  tasks.o(.bss)
    xPendingReadyList                        0x200008cc   Data          20  tasks.o(.bss)
    xTasksWaitingTermination                 0x200008e0   Data          20  tasks.o(.bss)
    xSuspendedTaskList                       0x200008f4   Data          20  tasks.o(.bss)
    .bss                                     0x20000908   Section    15360  heap_4.o(.bss)
    ucHeap                                   0x20000908   Data       15360  heap_4.o(.bss)
    STACK                                    0x20004508   Section     1024  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080000ed   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080000f1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080000f5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080000f5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080000f5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080000f5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x080000fd   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x08000101   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x08000101   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    SVC_Handler                              0x08000105   Thumb Code    32  port.o(.emb_text)
    __asm___6_port_c_39a90d8d__prvStartFirstTask 0x08000129   Thumb Code    28  port.o(.emb_text)
    PendSV_Handler                           0x08000149   Thumb Code    72  port.o(.emb_text)
    vPortGetIPSR                             0x08000195   Thumb Code     6  port.o(.emb_text)
    Reset_Handler                            0x0800019d   Thumb Code     8  startup_stm32f10x_md.o(.text)
    NMI_Handler                              0x080001a5   Thumb Code     2  startup_stm32f10x_md.o(.text)
    HardFault_Handler                        0x080001a7   Thumb Code     2  startup_stm32f10x_md.o(.text)
    MemManage_Handler                        0x080001a9   Thumb Code     2  startup_stm32f10x_md.o(.text)
    BusFault_Handler                         0x080001ab   Thumb Code     2  startup_stm32f10x_md.o(.text)
    UsageFault_Handler                       0x080001ad   Thumb Code     2  startup_stm32f10x_md.o(.text)
    DebugMon_Handler                         0x080001b1   Thumb Code     2  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM2_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x080001b7   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __aeabi_memset                           0x080001c1   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x080001c1   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x080001c1   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x080001cf   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x080001cf   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x080001cf   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x080001d3   Thumb Code    18  memseta.o(.text)
    __aeabi_fadd                             0x080001e5   Thumb Code   164  fadd.o(.text)
    __aeabi_fsub                             0x08000289   Thumb Code     6  fadd.o(.text)
    __aeabi_frsub                            0x0800028f   Thumb Code     6  fadd.o(.text)
    __aeabi_fmul                             0x08000295   Thumb Code   100  fmul.o(.text)
    __aeabi_fdiv                             0x080002f9   Thumb Code   124  fdiv.o(.text)
    __aeabi_dadd                             0x08000375   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080004b7   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080004bd   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080004c3   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080005a7   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2f                              0x08000685   Thumb Code    18  fflti.o(.text)
    __aeabi_ui2f                             0x08000697   Thumb Code    10  ffltui.o(.text)
    __aeabi_i2d                              0x080006a1   Thumb Code    34  dflti.o(.text)
    __aeabi_ui2d                             0x080006c3   Thumb Code    26  dfltui.o(.text)
    __aeabi_f2iz                             0x080006dd   Thumb Code    50  ffixi.o(.text)
    __aeabi_d2iz                             0x0800070f   Thumb Code    62  dfixi.o(.text)
    __aeabi_f2d                              0x0800074d   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x08000773   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x080007ab   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080007ab   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x080007d7   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x08000839   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000839   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000857   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000857   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08000877   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08000877   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x0800089b   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x0800089b   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x080008ad   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x08000909   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000927   Thumb Code   156  depilogue.o(.text)
    __aeabi_d2ulz                            0x080009c3   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdcmpeq                          0x080009f5   Thumb Code     0  cdcmple.o(.text)
    __aeabi_cdcmple                          0x080009f5   Thumb Code    48  cdcmple.o(.text)
    __aeabi_cdrcmple                         0x08000a25   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000a55   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000a55   Thumb Code     0  init.o(.text)
    __ARM_scalbn                             0x08000a79   Thumb Code    46  dscalb.o(.text)
    scalbn                                   0x08000a79   Thumb Code     0  dscalb.o(.text)
    __decompress                             0x08000aa7   Thumb Code     0  __dczerorl.o(.text)
    __decompress0                            0x08000aa7   Thumb Code    58  __dczerorl.o(.text)
    App_Car_Display                          0x08000ae1   Thumb Code   138  app_car.o(i.App_Car_Display)
    App_Car_GetAngle                         0x08000b9d   Thumb Code   162  app_car.o(i.App_Car_GetAngle)
    App_Car_PID                              0x08000c79   Thumb Code   236  app_car.o(i.App_Car_PID)
    App_Task_Display                         0x08000dad   Thumb Code    28  app_task.o(i.App_Task_Display)
    App_Task_GetData                         0x08000dc9   Thumb Code    46  app_task.o(i.App_Task_GetData)
    App_Task_Init                            0x08000dfd   Thumb Code    28  app_task.o(i.App_Task_Init)
    App_Task_PID                             0x08000e31   Thumb Code    20  app_task.o(i.App_Task_PID)
    App_Task_Start                           0x08000e45   Thumb Code    80  app_task.o(i.App_Task_Start)
    Com_Filter_Kalman                        0x08000ee5   Thumb Code   558  com_filter.o(i.Com_Filter_Kalman)
    Com_PID_Balance                          0x0800115d   Thumb Code    72  com_pid.o(i.Com_PID_Balance)
    Com_PID_Turn                             0x080011a5   Thumb Code    32  com_pid.o(i.Com_PID_Turn)
    Com_PID_Velocity                         0x080011c5   Thumb Code   192  com_pid.o(i.Com_PID_Velocity)
    Dri_TIM2_Init                            0x0800129d   Thumb Code   244  dri_tim.o(i.Dri_TIM2_Init)
    Dri_TIM3_Init                            0x080013a1   Thumb Code   266  dri_tim.o(i.Dri_TIM3_Init)
    Dri_TIM4_Init                            0x080014bd   Thumb Code   284  dri_tim.o(i.Dri_TIM4_Init)
    Driver_ADC1_Init                         0x080015e5   Thumb Code   252  dri_adc.o(i.Driver_ADC1_Init)
    Driver_ADC1_ReadV                        0x080016ed   Thumb Code    88  dri_adc.o(i.Driver_ADC1_ReadV)
    Driver_I2C2_Ack                          0x08001755   Thumb Code    14  dri_i2c.o(i.Driver_I2C2_Ack)
    Driver_I2C2_Init                         0x08001769   Thumb Code   110  dri_i2c.o(i.Driver_I2C2_Init)
    Driver_I2C2_NAck                         0x080017e5   Thumb Code    14  dri_i2c.o(i.Driver_I2C2_NAck)
    Driver_I2C2_Start                        0x080017f9   Thumb Code    48  dri_i2c.o(i.Driver_I2C2_Start)
    Driver_I2C2_Stop                         0x0800182d   Thumb Code    14  dri_i2c.o(i.Driver_I2C2_Stop)
    Driver_I2C_ReadByte                      0x08001841   Thumb Code    40  dri_i2c.o(i.Driver_I2C_ReadByte)
    Driver_I2C_SendAddr                      0x0800186d   Thumb Code    50  dri_i2c.o(i.Driver_I2C_SendAddr)
    Driver_I2C_SendByte                      0x080018a5   Thumb Code    66  dri_i2c.o(i.Driver_I2C_SendByte)
    Driver_SPI_Init                          0x080018ed   Thumb Code   106  dri_spi.o(i.Driver_SPI_Init)
    Driver_USART1_Init                       0x08001969   Thumb Code   276  dri_usart1.o(i.Driver_USART1_Init)
    Driver_USART2_Init                       0x08001a95   Thumb Code   266  dri_usart2.o(i.Driver_USART2_Init)
    Driver_USART2_SendChar                   0x08001bb9   Thumb Code    22  dri_usart2.o(i.Driver_USART2_SendChar)
    Driver_USART2_SendString                 0x08001bd5   Thumb Code    26  dri_usart2.o(i.Driver_USART2_SendString)
    Int_Encoder_Init                         0x08001bef   Thumb Code    12  int_encoder.o(i.Int_Encoder_Init)
    Int_Encoder_ReadCounter                  0x08001bfd   Thumb Code    52  int_encoder.o(i.Int_Encoder_ReadCounter)
    Int_MPU6050_Get_Accel                    0x08001c35   Thumb Code    68  int_mpu6050.o(i.Int_MPU6050_Get_Accel)
    Int_MPU6050_Get_Gyro                     0x08001c79   Thumb Code    68  int_mpu6050.o(i.Int_MPU6050_Get_Gyro)
    Int_MPU6050_Init                         0x08001cbd   Thumb Code   114  int_mpu6050.o(i.Int_MPU6050_Init)
    Int_MPU6050_ReadByte                     0x08001d2f   Thumb Code    50  int_mpu6050.o(i.Int_MPU6050_ReadByte)
    Int_MPU6050_ReadBytes                    0x08001d61   Thumb Code    80  int_mpu6050.o(i.Int_MPU6050_ReadBytes)
    Int_MPU6050_SetGyroRate                  0x08001db1   Thumb Code    52  int_mpu6050.o(i.Int_MPU6050_SetGyroRate)
    Int_MPU6050_Set_DLPF_CFG                 0x08001de5   Thumb Code    68  int_mpu6050.o(i.Int_MPU6050_Set_DLPF_CFG)
    Int_MPU6050_WriteByte                    0x08001e29   Thumb Code    36  int_mpu6050.o(i.Int_MPU6050_WriteByte)
    Int_TB6612_Init                          0x08001e4d   Thumb Code    32  int_tb6612.o(i.Int_TB6612_Init)
    Int_TB6612_MotorA                        0x08001e79   Thumb Code    78  int_tb6612.o(i.Int_TB6612_MotorA)
    Int_TB6612_MotorB                        0x08001ecd   Thumb Code    78  int_tb6612.o(i.Int_TB6612_MotorB)
    Int_TB6612_SetPWM                        0x08001f21   Thumb Code    82  int_tb6612.o(i.Int_TB6612_SetPWM)
    OLED_Clear                               0x08001f79   Thumb Code    42  oled.o(i.OLED_Clear)
    OLED_DrawPoint                           0x08001fa9   Thumb Code   114  oled.o(i.OLED_DrawPoint)
    OLED_Init                                0x08002021   Thumb Code   308  oled.o(i.OLED_Init)
    OLED_Refresh                             0x0800215d   Thumb Code    70  oled.o(i.OLED_Refresh)
    OLED_ShowChar                            0x080021a9   Thumb Code   300  oled.o(i.OLED_ShowChar)
    OLED_ShowString                          0x080022e5   Thumb Code    74  oled.o(i.OLED_ShowString)
    OLED_WR_Byte                             0x08002331   Thumb Code   140  oled.o(i.OLED_WR_Byte)
    SysTick_Handler                          0x080024ad   Thumb Code    16  main.o(i.SysTick_Handler)
    SystemInit                               0x080024bd   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    USART1_IRQHandler                        0x0800251d   Thumb Code    82  dri_usart1.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08002581   Thumb Code   182  app_car.o(i.USART2_IRQHandler)
    __0sprintf                               0x0800264d   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x0800264d   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x0800264d   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x0800264d   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x0800264d   Thumb Code     0  printfa.o(i.__0sprintf)
    __ARM_fpclassify                         0x08002675   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x0800269d   Thumb Code   170  poly.o(i.__kernel_poly)
    __mathlib_dbl_infnan                     0x08002747   Thumb Code     6  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x0800274d   Thumb Code     4  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_underflow                  0x08002751   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x08002761   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800276f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08002771   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08002781   Thumb Code     6  errno.o(i.__set_errno)
    atan                                     0x08003021   Thumb Code   474  atan.o(i.atan)
    atan2                                    0x08003241   Thumb Code   374  atan2.o(i.atan2)
    for_delay_ms                             0x080033dd   Thumb Code    24  main.o(i.for_delay_ms)
    main                                     0x080033f9   Thumb Code   110  main.o(i.main)
    pvPortMalloc                             0x080038b5   Thumb Code   208  heap_4.o(i.pvPortMalloc)
    pxPortInitialiseStack                    0x08003999   Thumb Code    30  port.o(i.pxPortInitialiseStack)
    ulTaskGenericNotifyTake                  0x080039bd   Thumb Code   128  tasks.o(i.ulTaskGenericNotifyTake)
    uxListRemove                             0x08003a45   Thumb Code    40  list.o(i.uxListRemove)
    vListInitialise                          0x08003a6d   Thumb Code    26  list.o(i.vListInitialise)
    vListInitialiseItem                      0x08003a87   Thumb Code     6  list.o(i.vListInitialiseItem)
    vListInsert                              0x08003a8d   Thumb Code    52  list.o(i.vListInsert)
    vListInsertEnd                           0x08003ac1   Thumb Code    24  list.o(i.vListInsertEnd)
    vPortEnterCritical                       0x08003ad9   Thumb Code    30  port.o(i.vPortEnterCritical)
    vPortExitCritical                        0x08003afd   Thumb Code    28  port.o(i.vPortExitCritical)
    vPortFree                                0x08003b1d   Thumb Code    68  heap_4.o(i.vPortFree)
    vPortSetupTimerInterrupt                 0x08003b69   Thumb Code    20  port.o(i.vPortSetupTimerInterrupt)
    vTaskDelete                              0x08003b81   Thumb Code   174  tasks.o(i.vTaskDelete)
    vTaskStartScheduler                      0x08003c55   Thumb Code    76  tasks.o(i.vTaskStartScheduler)
    vTaskSuspendAll                          0x08003cc1   Thumb Code    12  tasks.o(i.vTaskSuspendAll)
    vTaskSwitchContext                       0x08003cd1   Thumb Code    76  tasks.o(i.vTaskSwitchContext)
    xPortStartScheduler                      0x08003d31   Thumb Code    42  port.o(i.xPortStartScheduler)
    xPortSysTickHandler                      0x08003d65   Thumb Code    46  port.o(i.xPortSysTickHandler)
    xTaskCreate                              0x08003d99   Thumb Code   104  tasks.o(i.xTaskCreate)
    xTaskDelayUntil                          0x08003e01   Thumb Code   108  tasks.o(i.xTaskDelayUntil)
    xTaskGenericNotify                       0x08003e75   Thumb Code   314  tasks.o(i.xTaskGenericNotify)
    xTaskGetSchedulerState                   0x08003fc1   Thumb Code    24  tasks.o(i.xTaskGetSchedulerState)
    xTaskGetTickCount                        0x08003fe1   Thumb Code     6  tasks.o(i.xTaskGetTickCount)
    xTaskIncrementTick                       0x08003fed   Thumb Code   356  tasks.o(i.xTaskIncrementTick)
    xTaskResumeAll                           0x0800417d   Thumb Code   310  tasks.o(i.xTaskResumeAll)
    asc2_0806                                0x080042d8   Data         552  oled.o(.constdata)
    asc2_1206                                0x08004500   Data        1140  oled.o(.constdata)
    asc2_1608                                0x08004974   Data        1520  oled.o(.constdata)
    asc2_2412                                0x08004f64   Data        3420  oled.o(.constdata)
    Hzk1                                     0x08005cc0   Data         352  oled.o(.constdata)
    Hzk2                                     0x08005e20   Data          72  oled.o(.constdata)
    Hzk3                                     0x08005e68   Data         128  oled.o(.constdata)
    Hzk4                                     0x08005ee8   Data         512  oled.o(.constdata)
    __mathlib_zero                           0x08006180   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x08006188   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080061a8   Number         0  anon$$obj.o(Region$$Table)
    len                                      0x20000000   Data           1  dri_usart1.o(.data)
    isToSend                                 0x20000001   Data           1  dri_usart1.o(.data)
    K1                                       0x20000004   Data           4  com_filter.o(.data)
    angle                                    0x20000008   Data           4  com_filter.o(.data)
    angle_dot                                0x2000000c   Data           4  com_filter.o(.data)
    Q_angle                                  0x20000010   Data           4  com_filter.o(.data)
    Q_gyro                                   0x20000014   Data           4  com_filter.o(.data)
    R_angle                                  0x20000018   Data           4  com_filter.o(.data)
    dt                                       0x2000001c   Data           4  com_filter.o(.data)
    C_0                                      0x20000020   Data           1  com_filter.o(.data)
    Q_bias                                   0x20000024   Data           4  com_filter.o(.data)
    Angle_err                                0x20000028   Data           4  com_filter.o(.data)
    PCt_0                                    0x2000002c   Data           4  com_filter.o(.data)
    PCt_1                                    0x20000030   Data           4  com_filter.o(.data)
    E                                        0x20000034   Data           4  com_filter.o(.data)
    K_0                                      0x20000038   Data           4  com_filter.o(.data)
    K_1                                      0x2000003c   Data           4  com_filter.o(.data)
    t_0                                      0x20000040   Data           4  com_filter.o(.data)
    t_1                                      0x20000044   Data           4  com_filter.o(.data)
    PP                                       0x20000048   Data          16  com_filter.o(.data)
    gx                                       0x20000060   Data           2  app_car.o(.data)
    gy                                       0x20000062   Data           2  app_car.o(.data)
    gz                                       0x20000064   Data           2  app_car.o(.data)
    ax                                       0x20000066   Data           2  app_car.o(.data)
    ay                                       0x20000068   Data           2  app_car.o(.data)
    az                                       0x2000006a   Data           2  app_car.o(.data)
    accel_angle                              0x2000006c   Data           4  app_car.o(.data)
    gyro_y                                   0x20000070   Data           4  app_car.o(.data)
    ea                                       0x20000074   Data           4  app_car.o(.data)
    eb                                       0x20000078   Data           4  app_car.o(.data)
    bat_str                                  0x2000007c   Data           5  app_car.o(.data)
    ea_str                                   0x20000081   Data           7  app_car.o(.data)
    eb_str                                   0x20000088   Data           7  app_car.o(.data)
    angle_str                                0x2000008f   Data           7  app_car.o(.data)
    balance_kp                               0x20000098   Data           4  app_car.o(.data)
    balance_kd                               0x2000009c   Data           4  app_car.o(.data)
    balance_angle                            0x200000a0   Data           4  app_car.o(.data)
    velocity_kp                              0x200000a4   Data           4  app_car.o(.data)
    velocity_ki                              0x200000a8   Data           4  app_car.o(.data)
    turn_kp                                  0x200000ac   Data           4  app_car.o(.data)
    flag_up                                  0x200000b0   Data           1  app_car.o(.data)
    flag_down                                0x200000b1   Data           1  app_car.o(.data)
    flag_left                                0x200000b2   Data           1  app_car.o(.data)
    flag_right                               0x200000b3   Data           1  app_car.o(.data)
    remote_move                              0x200000b4   Data           4  app_car.o(.data)
    remote_turn                              0x200000b8   Data           4  app_car.o(.data)
    start_task_handle                        0x200000bc   Data           4  app_task.o(.data)
    data_task_handle                         0x200000c0   Data           4  app_task.o(.data)
    pid_task_handle                          0x200000c4   Data           4  app_task.o(.data)
    display_task_handle                      0x200000c8   Data           4  app_task.o(.data)
    pxCurrentTCB                             0x200000cc   Data           4  tasks.o(.data)
    uxTopUsedPriority                        0x20000104   Data           4  tasks.o(.data)
    buff                                     0x20000130   Data         100  dri_usart1.o(.bss)
    OLED_GRAM                                0x20000194   Data        1152  oled.o(.bss)
    Pdot                                     0x20000614   Data          16  com_filter.o(.bss)
    __initial_sp                             0x20004908   Data           0  startup_stm32f10x_md.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000062d8, Max: 0x00010000, ABSOLUTE, COMPRESSED[0x000061e4])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000061a8, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO           19    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000000   Code   RO         1663  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080000ec   0x080000ec   0x00000004   Code   RO         1986    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080000f0   0x080000f0   0x00000004   Code   RO         1989    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         1991    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080000f4   0x080000f4   0x00000000   Code   RO         1993    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080000f4   0x080000f4   0x00000008   Code   RO         1994    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080000fc   0x080000fc   0x00000004   Code   RO         2001    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x08000100   0x08000100   0x00000000   Code   RO         1996    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x08000100   0x08000100   0x00000000   Code   RO         1998    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x08000100   0x08000100   0x00000004   Code   RO         1987    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000104   0x08000104   0x00000096   Code   RO         1584    .emb_text           port.o
    0x0800019a   0x0800019a   0x00000002   PAD
    0x0800019c   0x0800019c   0x00000024   Code   RO           20    .text               startup_stm32f10x_md.o
    0x080001c0   0x080001c0   0x00000024   Code   RO         1668    .text               mc_w.l(memseta.o)
    0x080001e4   0x080001e4   0x000000b0   Code   RO         1933    .text               mf_w.l(fadd.o)
    0x08000294   0x08000294   0x00000064   Code   RO         1935    .text               mf_w.l(fmul.o)
    0x080002f8   0x080002f8   0x0000007c   Code   RO         1937    .text               mf_w.l(fdiv.o)
    0x08000374   0x08000374   0x0000014e   Code   RO         1939    .text               mf_w.l(dadd.o)
    0x080004c2   0x080004c2   0x000000e4   Code   RO         1941    .text               mf_w.l(dmul.o)
    0x080005a6   0x080005a6   0x000000de   Code   RO         1943    .text               mf_w.l(ddiv.o)
    0x08000684   0x08000684   0x00000012   Code   RO         1945    .text               mf_w.l(fflti.o)
    0x08000696   0x08000696   0x0000000a   Code   RO         1947    .text               mf_w.l(ffltui.o)
    0x080006a0   0x080006a0   0x00000022   Code   RO         1949    .text               mf_w.l(dflti.o)
    0x080006c2   0x080006c2   0x0000001a   Code   RO         1951    .text               mf_w.l(dfltui.o)
    0x080006dc   0x080006dc   0x00000032   Code   RO         1953    .text               mf_w.l(ffixi.o)
    0x0800070e   0x0800070e   0x0000003e   Code   RO         1955    .text               mf_w.l(dfixi.o)
    0x0800074c   0x0800074c   0x00000026   Code   RO         1957    .text               mf_w.l(f2d.o)
    0x08000772   0x08000772   0x00000038   Code   RO         1959    .text               mf_w.l(d2f.o)
    0x080007aa   0x080007aa   0x0000002c   Code   RO         2003    .text               mc_w.l(uidiv.o)
    0x080007d6   0x080007d6   0x00000062   Code   RO         2005    .text               mc_w.l(uldiv.o)
    0x08000838   0x08000838   0x0000001e   Code   RO         2007    .text               mc_w.l(llshl.o)
    0x08000856   0x08000856   0x00000020   Code   RO         2009    .text               mc_w.l(llushr.o)
    0x08000876   0x08000876   0x00000024   Code   RO         2011    .text               mc_w.l(llsshr.o)
    0x0800089a   0x0800089a   0x00000000   Code   RO         2020    .text               mc_w.l(iusefp.o)
    0x0800089a   0x0800089a   0x0000006e   Code   RO         2021    .text               mf_w.l(fepilogue.o)
    0x08000908   0x08000908   0x000000ba   Code   RO         2023    .text               mf_w.l(depilogue.o)
    0x080009c2   0x080009c2   0x00000030   Code   RO         2025    .text               mf_w.l(dfixul.o)
    0x080009f2   0x080009f2   0x00000002   PAD
    0x080009f4   0x080009f4   0x00000030   Code   RO         2027    .text               mf_w.l(cdcmple.o)
    0x08000a24   0x08000a24   0x00000030   Code   RO         2029    .text               mf_w.l(cdrcmple.o)
    0x08000a54   0x08000a54   0x00000024   Code   RO         2035    .text               mc_w.l(init.o)
    0x08000a78   0x08000a78   0x0000002e   Code   RO         2037    .text               mf_w.l(dscalb.o)
    0x08000aa6   0x08000aa6   0x0000003a   Code   RO         2047    .text               mc_w.l(__dczerorl.o)
    0x08000ae0   0x08000ae0   0x000000bc   Code   RO          709    i.App_Car_Display   app_car.o
    0x08000b9c   0x08000b9c   0x000000dc   Code   RO          710    i.App_Car_GetAngle  app_car.o
    0x08000c78   0x08000c78   0x00000134   Code   RO          711    i.App_Car_PID       app_car.o
    0x08000dac   0x08000dac   0x0000001c   Code   RO          746    i.App_Task_Display  app_task.o
    0x08000dc8   0x08000dc8   0x00000034   Code   RO          747    i.App_Task_GetData  app_task.o
    0x08000dfc   0x08000dfc   0x00000034   Code   RO          748    i.App_Task_Init     app_task.o
    0x08000e30   0x08000e30   0x00000014   Code   RO          749    i.App_Task_PID      app_task.o
    0x08000e44   0x08000e44   0x000000a0   Code   RO          750    i.App_Task_Start    app_task.o
    0x08000ee4   0x08000ee4   0x00000278   Code   RO          662    i.Com_Filter_Kalman  com_filter.o
    0x0800115c   0x0800115c   0x00000048   Code   RO          681    i.Com_PID_Balance   com_pid.o
    0x080011a4   0x080011a4   0x00000020   Code   RO          682    i.Com_PID_Turn      com_pid.o
    0x080011c4   0x080011c4   0x000000d8   Code   RO          683    i.Com_PID_Velocity  com_pid.o
    0x0800129c   0x0800129c   0x00000104   Code   RO          186    i.Dri_TIM2_Init     dri_tim.o
    0x080013a0   0x080013a0   0x0000011c   Code   RO          187    i.Dri_TIM3_Init     dri_tim.o
    0x080014bc   0x080014bc   0x00000128   Code   RO          188    i.Dri_TIM4_Init     dri_tim.o
    0x080015e4   0x080015e4   0x00000108   Code   RO          336    i.Driver_ADC1_Init  dri_adc.o
    0x080016ec   0x080016ec   0x00000068   Code   RO          337    i.Driver_ADC1_ReadV  dri_adc.o
    0x08001754   0x08001754   0x00000014   Code   RO          279    i.Driver_I2C2_Ack   dri_i2c.o
    0x08001768   0x08001768   0x0000007c   Code   RO          280    i.Driver_I2C2_Init  dri_i2c.o
    0x080017e4   0x080017e4   0x00000014   Code   RO          281    i.Driver_I2C2_NAck  dri_i2c.o
    0x080017f8   0x080017f8   0x00000034   Code   RO          282    i.Driver_I2C2_Start  dri_i2c.o
    0x0800182c   0x0800182c   0x00000014   Code   RO          283    i.Driver_I2C2_Stop  dri_i2c.o
    0x08001840   0x08001840   0x0000002c   Code   RO          284    i.Driver_I2C_ReadByte  dri_i2c.o
    0x0800186c   0x0800186c   0x00000038   Code   RO          285    i.Driver_I2C_SendAddr  dri_i2c.o
    0x080018a4   0x080018a4   0x00000048   Code   RO          286    i.Driver_I2C_SendByte  dri_i2c.o
    0x080018ec   0x080018ec   0x0000007c   Code   RO          357    i.Driver_SPI_Init   dri_spi.o
    0x08001968   0x08001968   0x0000012c   Code   RO          213    i.Driver_USART1_Init  dri_usart1.o
    0x08001a94   0x08001a94   0x00000124   Code   RO          378    i.Driver_USART2_Init  dri_usart2.o
    0x08001bb8   0x08001bb8   0x0000001c   Code   RO          381    i.Driver_USART2_SendChar  dri_usart2.o
    0x08001bd4   0x08001bd4   0x0000001a   Code   RO          382    i.Driver_USART2_SendString  dri_usart2.o
    0x08001bee   0x08001bee   0x0000000c   Code   RO          451    i.Int_Encoder_Init  int_encoder.o
    0x08001bfa   0x08001bfa   0x00000002   PAD
    0x08001bfc   0x08001bfc   0x00000038   Code   RO          452    i.Int_Encoder_ReadCounter  int_encoder.o
    0x08001c34   0x08001c34   0x00000044   Code   RO          472    i.Int_MPU6050_Get_Accel  int_mpu6050.o
    0x08001c78   0x08001c78   0x00000044   Code   RO          473    i.Int_MPU6050_Get_Gyro  int_mpu6050.o
    0x08001cbc   0x08001cbc   0x00000072   Code   RO          474    i.Int_MPU6050_Init  int_mpu6050.o
    0x08001d2e   0x08001d2e   0x00000032   Code   RO          475    i.Int_MPU6050_ReadByte  int_mpu6050.o
    0x08001d60   0x08001d60   0x00000050   Code   RO          476    i.Int_MPU6050_ReadBytes  int_mpu6050.o
    0x08001db0   0x08001db0   0x00000034   Code   RO          477    i.Int_MPU6050_SetGyroRate  int_mpu6050.o
    0x08001de4   0x08001de4   0x00000044   Code   RO          478    i.Int_MPU6050_Set_DLPF_CFG  int_mpu6050.o
    0x08001e28   0x08001e28   0x00000024   Code   RO          479    i.Int_MPU6050_WriteByte  int_mpu6050.o
    0x08001e4c   0x08001e4c   0x0000002c   Code   RO          418    i.Int_TB6612_Init   int_tb6612.o
    0x08001e78   0x08001e78   0x00000054   Code   RO          419    i.Int_TB6612_MotorA  int_tb6612.o
    0x08001ecc   0x08001ecc   0x00000054   Code   RO          420    i.Int_TB6612_MotorB  int_tb6612.o
    0x08001f20   0x08001f20   0x00000058   Code   RO          421    i.Int_TB6612_SetPWM  int_tb6612.o
    0x08001f78   0x08001f78   0x00000030   Code   RO          535    i.OLED_Clear        oled.o
    0x08001fa8   0x08001fa8   0x00000078   Code   RO          542    i.OLED_DrawPoint    oled.o
    0x08002020   0x08002020   0x0000013c   Code   RO          543    i.OLED_Init         oled.o
    0x0800215c   0x0800215c   0x0000004c   Code   RO          545    i.OLED_Refresh      oled.o
    0x080021a8   0x080021a8   0x0000013c   Code   RO          547    i.OLED_ShowChar     oled.o
    0x080022e4   0x080022e4   0x0000004a   Code   RO          551    i.OLED_ShowString   oled.o
    0x0800232e   0x0800232e   0x00000002   PAD
    0x08002330   0x08002330   0x00000094   Code   RO          552    i.OLED_WR_Byte      oled.o
    0x080023c4   0x080023c4   0x00000008   Code   RO           24    i.SetSysClock       system_stm32f10x.o
    0x080023cc   0x080023cc   0x000000e0   Code   RO           25    i.SetSysClockTo72   system_stm32f10x.o
    0x080024ac   0x080024ac   0x00000010   Code   RO           65    i.SysTick_Handler   main.o
    0x080024bc   0x080024bc   0x00000060   Code   RO           27    i.SystemInit        system_stm32f10x.o
    0x0800251c   0x0800251c   0x00000064   Code   RO          218    i.USART1_IRQHandler  dri_usart1.o
    0x08002580   0x08002580   0x000000cc   Code   RO          712    i.USART2_IRQHandler  app_car.o
    0x0800264c   0x0800264c   0x00000028   Code   RO         1907    i.__0sprintf        mc_w.l(printfa.o)
    0x08002674   0x08002674   0x00000028   Code   RO         2031    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x0800269c   0x0800269c   0x000000aa   Code   RO         2033    i.__kernel_poly     m_ws.l(poly.o)
    0x08002746   0x08002746   0x00000006   Code   RO         1972    i.__mathlib_dbl_infnan  m_ws.l(dunder.o)
    0x0800274c   0x0800274c   0x00000004   Code   RO         1973    i.__mathlib_dbl_infnan2  m_ws.l(dunder.o)
    0x08002750   0x08002750   0x00000010   Code   RO         1977    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x08002760   0x08002760   0x0000000e   Code   RO         2041    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800276e   0x0800276e   0x00000002   Code   RO         2042    i.__scatterload_null  mc_w.l(handlers.o)
    0x08002770   0x08002770   0x0000000e   Code   RO         2043    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800277e   0x0800277e   0x00000002   PAD
    0x08002780   0x08002780   0x0000000c   Code   RO         2015    i.__set_errno       mc_w.l(errno.o)
    0x0800278c   0x0800278c   0x00000184   Code   RO         1912    i._fp_digits        mc_w.l(printfa.o)
    0x08002910   0x08002910   0x000006b4   Code   RO         1913    i._printf_core      mc_w.l(printfa.o)
    0x08002fc4   0x08002fc4   0x00000024   Code   RO         1914    i._printf_post_padding  mc_w.l(printfa.o)
    0x08002fe8   0x08002fe8   0x0000002e   Code   RO         1915    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08003016   0x08003016   0x0000000a   Code   RO         1917    i._sputc            mc_w.l(printfa.o)
    0x08003020   0x08003020   0x00000220   Code   RO         1962    i.atan              m_ws.l(atan.o)
    0x08003240   0x08003240   0x0000019c   Code   RO         1656    i.atan2             m_ws.l(atan2.o)
    0x080033dc   0x080033dc   0x0000001c   Code   RO           66    i.for_delay_ms      main.o
    0x080033f8   0x080033f8   0x00000090   Code   RO           67    i.main              main.o
    0x08003488   0x08003488   0x000000c0   Code   RO         1211    i.prvAddCurrentTaskToDelayedList  tasks.o
    0x08003548   0x08003548   0x000000e4   Code   RO         1212    i.prvAddNewTaskToReadyList  tasks.o
    0x0800362c   0x0800362c   0x00000048   Code   RO         1213    i.prvCheckTasksWaitingTermination  tasks.o
    0x08003674   0x08003674   0x00000012   Code   RO         1214    i.prvDeleteTCB      tasks.o
    0x08003686   0x08003686   0x00000002   PAD
    0x08003688   0x08003688   0x00000070   Code   RO         1523    i.prvHeapInit       heap_4.o
    0x080036f8   0x080036f8   0x00000028   Code   RO         1215    i.prvIdleTask       tasks.o
    0x08003720   0x08003720   0x0000007a   Code   RO         1216    i.prvInitialiseNewTask  tasks.o
    0x0800379a   0x0800379a   0x00000002   PAD
    0x0800379c   0x0800379c   0x00000068   Code   RO         1217    i.prvInitialiseTaskLists  tasks.o
    0x08003804   0x08003804   0x00000068   Code   RO         1524    i.prvInsertBlockIntoFreeList  heap_4.o
    0x0800386c   0x0800386c   0x00000030   Code   RO         1218    i.prvResetNextTaskUnblockTime  tasks.o
    0x0800389c   0x0800389c   0x00000016   Code   RO         1585    i.prvTaskExitError  port.o
    0x080038b2   0x080038b2   0x00000002   PAD
    0x080038b4   0x080038b4   0x000000e4   Code   RO         1526    i.pvPortMalloc      heap_4.o
    0x08003998   0x08003998   0x00000024   Code   RO         1586    i.pxPortInitialiseStack  port.o
    0x080039bc   0x080039bc   0x00000088   Code   RO         1220    i.ulTaskGenericNotifyTake  tasks.o
    0x08003a44   0x08003a44   0x00000028   Code   RO          892    i.uxListRemove      list.o
    0x08003a6c   0x08003a6c   0x0000001a   Code   RO          893    i.vListInitialise   list.o
    0x08003a86   0x08003a86   0x00000006   Code   RO          894    i.vListInitialiseItem  list.o
    0x08003a8c   0x08003a8c   0x00000034   Code   RO          895    i.vListInsert       list.o
    0x08003ac0   0x08003ac0   0x00000018   Code   RO          896    i.vListInsertEnd    list.o
    0x08003ad8   0x08003ad8   0x00000024   Code   RO         1588    i.vPortEnterCritical  port.o
    0x08003afc   0x08003afc   0x00000020   Code   RO         1589    i.vPortExitCritical  port.o
    0x08003b1c   0x08003b1c   0x0000004c   Code   RO         1527    i.vPortFree         heap_4.o
    0x08003b68   0x08003b68   0x00000018   Code   RO         1590    i.vPortSetupTimerInterrupt  port.o
    0x08003b80   0x08003b80   0x000000d4   Code   RO         1227    i.vTaskDelete       tasks.o
    0x08003c54   0x08003c54   0x0000006c   Code   RO         1238    i.vTaskStartScheduler  tasks.o
    0x08003cc0   0x08003cc0   0x00000010   Code   RO         1240    i.vTaskSuspendAll   tasks.o
    0x08003cd0   0x08003cd0   0x00000060   Code   RO         1241    i.vTaskSwitchContext  tasks.o
    0x08003d30   0x08003d30   0x00000034   Code   RO         1591    i.xPortStartScheduler  port.o
    0x08003d64   0x08003d64   0x00000034   Code   RO         1592    i.xPortSysTickHandler  port.o
    0x08003d98   0x08003d98   0x00000068   Code   RO         1244    i.xTaskCreate       tasks.o
    0x08003e00   0x08003e00   0x00000074   Code   RO         1245    i.xTaskDelayUntil   tasks.o
    0x08003e74   0x08003e74   0x0000014c   Code   RO         1246    i.xTaskGenericNotify  tasks.o
    0x08003fc0   0x08003fc0   0x00000020   Code   RO         1251    i.xTaskGetSchedulerState  tasks.o
    0x08003fe0   0x08003fe0   0x0000000c   Code   RO         1252    i.xTaskGetTickCount  tasks.o
    0x08003fec   0x08003fec   0x00000190   Code   RO         1254    i.xTaskIncrementTick  tasks.o
    0x0800417c   0x0800417c   0x0000015c   Code   RO         1256    i.xTaskResumeAll    tasks.o
    0x080042d8   0x080042d8   0x00001e10   Data   RO          554    .constdata          oled.o
    0x080060e8   0x080060e8   0x00000098   Data   RO         1963    .constdata          m_ws.l(atan.o)
    0x08006180   0x08006180   0x00000008   Data   RO         1985    .constdata          m_ws.l(qnan.o)
    0x08006188   0x08006188   0x00000020   Data   RO         2039    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080061a8, Size: 0x00004908, Max: 0x00005000, ABSOLUTE, COMPRESSED[0x0000003c])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000002   Data   RW          221    .data               dri_usart1.o
    0x20000002   COMPRESSED   0x00000002   PAD
    0x20000004   COMPRESSED   0x00000054   Data   RW          664    .data               com_filter.o
    0x20000058   COMPRESSED   0x00000008   Data   RW          684    .data               com_pid.o
    0x20000060   COMPRESSED   0x0000005c   Data   RW          713    .data               app_car.o
    0x200000bc   COMPRESSED   0x00000010   Data   RW          751    .data               app_task.o
    0x200000cc   COMPRESSED   0x00000040   Data   RW         1259    .data               tasks.o
    0x2000010c   COMPRESSED   0x0000001c   Data   RW         1533    .data               heap_4.o
    0x20000128   COMPRESSED   0x00000004   Data   RW         1593    .data               port.o
    0x2000012c   COMPRESSED   0x00000004   Data   RW         2016    .data               mc_w.l(errno.o)
    0x20000130        -       0x00000064   Zero   RW          220    .bss                dri_usart1.o
    0x20000194        -       0x00000480   Zero   RW          553    .bss                oled.o
    0x20000614        -       0x00000010   Zero   RW          663    .bss                com_filter.o
    0x20000624        -       0x000002e4   Zero   RW         1258    .bss                tasks.o
    0x20000908        -       0x00003c00   Zero   RW         1532    .bss                heap_4.o
    0x20004508        -       0x00000400   Zero   RW           17    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       920        202          0         92          0       4071   app_car.o
       312        110          0         16          0       3183   app_task.o
       632         74          0         84         16       1914   com_filter.o
       320         24          0          8          0       2312   com_pid.o
         0          0          0          0          0       4604   core_cm3.o
       368         28          0          0          0       1095   dri_adc.o
       408         52          0          0          0       4341   dri_i2c.o
       124         18          0          0          0        490   dri_spi.o
       840         46          0          0          0       1652   dri_tim.o
       400         42          0          2        100      17344   dri_usart1.o
       346         32          0          0          0       2521   dri_usart2.o
         0          0          0          0          0       1100   event_groups.o
       520         58          0         28      15360       4971   heap_4.o
        68          4          0          0          0       1105   int_encoder.o
       536          0          0          0          0       5766   int_mpu6050.o
       300         30          0          0          0       2238   int_tb6612.o
       148          0          0          0          0       3650   list.o
       188         38          0          0          0     219308   main.o
      1098         50       7696          0       1152       8046   oled.o
       404         48          0          4          0       9568   port.o
        36          8        236          0       1024        812   startup_stm32f10x_md.o
       328         28          0          0          0       2149   system_stm32f10x.o
      2736        348          0         64        740      31166   tasks.o

    ----------------------------------------------------------------------
     11044       <USER>       <GROUP>        300      18392     333406   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        12          0          0          2          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       544         70        152          0          0        124   atan.o
       412         38          0          0          0        144   atan2.o
        26          6          0          0          0        204   dunder.o
        40          0          0          0          0         68   fpclassify.o
       170          0          0          0          0         96   poly.o
         0          0          8          0          0          0   qnan.o
        58          0          0          0          0          0   __dczerorl.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      2236         86          0          0          0        532   printfa.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdcmple.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
        46          0          0          0          0         80   dscalb.o
        38          0          0          0          0         68   f2d.o
       176          0          0          0          0        140   fadd.o
       124          0          0          0          0         88   fdiv.o
       110          0          0          0          0        168   fepilogue.o
        50          0          0          0          0         68   ffixi.o
        18          0          0          0          0         68   fflti.o
        10          0          0          0          0         68   ffltui.o
       100          0          0          0          0         76   fmul.o

    ----------------------------------------------------------------------
      5832        <USER>        <GROUP>          4          0       3656   Library Totals
         4          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1192        114        160          0          0        636   m_ws.l
      2672        108          0          4          0       1152   mc_w.l
      1964          0          0          0          0       1868   mf_w.l

    ----------------------------------------------------------------------
      5832        <USER>        <GROUP>          4          0       3656   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     16876       1462       8124        304      18392     330430   Grand Totals
     16876       1462       8124         60      18392     330430   ELF Image Totals (compressed)
     16876       1462       8124         60          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                25000 (  24.41kB)
    Total RW  Size (RW Data + ZI Data)             18696 (  18.26kB)
    Total ROM Size (Code + RO Data + RW Data)      25060 (  24.47kB)

==============================================================================

