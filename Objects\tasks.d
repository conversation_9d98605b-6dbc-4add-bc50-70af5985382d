.\objects\tasks.o: Mid\FreeRTOS\Source\tasks.c
.\objects\tasks.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stdlib.h
.\objects\tasks.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\string.h
.\objects\tasks.o: .\Mid\FreeRTOS\include\FreeRTOS.h
.\objects\tasks.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stddef.h
.\objects\tasks.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stdint.h
.\objects\tasks.o: .\Mid\FreeRTOS\FreeRTOSConfig.h
.\objects\tasks.o: .\Mid\FreeRTOS\include\projdefs.h
.\objects\tasks.o: .\Mid\FreeRTOS\include\portable.h
.\objects\tasks.o: .\Mid\FreeRTOS\include\deprecated_definitions.h
.\objects\tasks.o: .\Mid\FreeRTOS\Portable\portmacro.h
.\objects\tasks.o: .\Mid\FreeRTOS\include\mpu_wrappers.h
.\objects\tasks.o: .\Mid\FreeRTOS\include\task.h
.\objects\tasks.o: .\Mid\FreeRTOS\include\list.h
.\objects\tasks.o: .\Mid\FreeRTOS\include\timers.h
.\objects\tasks.o: .\Mid\FreeRTOS\include\stack_macros.h
