.\objects\queue.o: Mid\FreeRTOS\Source\queue.c
.\objects\queue.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stdlib.h
.\objects\queue.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\string.h
.\objects\queue.o: .\Mid\FreeRTOS\include\FreeRTOS.h
.\objects\queue.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stddef.h
.\objects\queue.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stdint.h
.\objects\queue.o: .\Mid\FreeRTOS\FreeRTOSConfig.h
.\objects\queue.o: .\Mid\FreeRTOS\include\projdefs.h
.\objects\queue.o: .\Mid\FreeRTOS\include\portable.h
.\objects\queue.o: .\Mid\FreeRTOS\include\deprecated_definitions.h
.\objects\queue.o: .\Mid\FreeRTOS\Portable\portmacro.h
.\objects\queue.o: .\Mid\FreeRTOS\include\mpu_wrappers.h
.\objects\queue.o: .\Mid\FreeRTOS\include\task.h
.\objects\queue.o: .\Mid\FreeRTOS\include\list.h
.\objects\queue.o: .\Mid\FreeRTOS\include\queue.h
