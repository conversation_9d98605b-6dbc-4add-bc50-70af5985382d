.\objects\app_car.o: App\App_Car.c
.\objects\app_car.o: App\App_Car.h
.\objects\app_car.o: .\Int\Int_MPU6050.h
.\objects\app_car.o: .\Start\stm32f10x.h
.\objects\app_car.o: .\Start\core_cm3.h
.\objects\app_car.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stdint.h
.\objects\app_car.o: .\Start\system_stm32f10x.h
.\objects\app_car.o: .\Dri\Dri_I2C.h
.\objects\app_car.o: .\Com\Com_Filter.h
.\objects\app_car.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\math.h
.\objects\app_car.o: .\Dri\Dri_ADC.h
.\objects\app_car.o: .\Int\OLED\oled.h
.\objects\app_car.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stdlib.h
.\objects\app_car.o: .\Dri\Dri_SPI.h
.\objects\app_car.o: .\Int\Int_Encoder.h
.\objects\app_car.o: .\Dri\Dri_TIM.h
.\objects\app_car.o: .\Com\Com_PID.h
.\objects\app_car.o: .\Int\Int_TB6612.h
