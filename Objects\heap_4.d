.\objects\heap_4.o: Mid\FreeRTOS\Portable\heap_4.c
.\objects\heap_4.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stdlib.h
.\objects\heap_4.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\string.h
.\objects\heap_4.o: .\Mid\FreeRTOS\include\FreeRTOS.h
.\objects\heap_4.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stddef.h
.\objects\heap_4.o: C:\Users\<USER>\AppData\Local\Keil_v5\ARM\arm5_compiler\Bin\..\include\stdint.h
.\objects\heap_4.o: .\Mid\FreeRTOS\FreeRTOSConfig.h
.\objects\heap_4.o: .\Mid\FreeRTOS\include\projdefs.h
.\objects\heap_4.o: .\Mid\FreeRTOS\include\portable.h
.\objects\heap_4.o: .\Mid\FreeRTOS\include\deprecated_definitions.h
.\objects\heap_4.o: .\Mid\FreeRTOS\Portable\portmacro.h
.\objects\heap_4.o: .\Mid\FreeRTOS\include\mpu_wrappers.h
.\objects\heap_4.o: .\Mid\FreeRTOS\include\task.h
.\objects\heap_4.o: .\Mid\FreeRTOS\include\list.h
